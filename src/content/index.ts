/**
 * Content 模块统一导出文件
 * 集成新的轻量化翻译系统
 */

// 导出轻量化翻译系统
export * from '../index';

// 导出原有管理器类型（向后兼容）
export type { HighlightManager, HighlightManagerOptions } from './highlight-manager';
export type { TooltipManager, TooltipManagerOptions } from './tooltip-manager';
export type { SliderManager, SliderManagerOptions } from './slider-manager';
export type { InteractionHandlers, InteractionHandlersOptions, ManagerInstances } from './interaction-handlers';
// DebugFunctions 类型已移除

// 导出初始化函数
export { initializeHighlighting } from './highlight-manager';
export { initializeTooltips } from './tooltip-manager';
export { initializeSlider } from './slider-manager';
export { setupInteractionHandlers } from './interaction-handlers';
// initializeDebugFunctions 导出已移除

// 统一的管理器接口
export interface LucidManagers {
  highlightManager: import('./highlight-manager').HighlightManager;
  tooltipManager: import('./tooltip-manager').TooltipManager;
  sliderManager: import('./slider-manager').SliderManager;
  interactionHandlers: import('./interaction-handlers').InteractionHandlers;
  // debugFunctions 属性已移除
}

/**
 * 一键初始化所有 Lucid 管理器
 */
export async function initializeLucidManagers(): Promise<LucidManagers> {
  const { initializeHighlighting } = await import('./highlight-manager');
  const { initializeTooltips } = await import('./tooltip-manager');
  const { initializeSlider } = await import('./slider-manager');
  const { setupInteractionHandlers } = await import('./interaction-handlers');
  // initializeDebugFunctions 调用已移除

  // 1. 初始化高亮管理器（基础功能）
  const highlightManager = await initializeHighlighting({
    effects: ['lu-gradient', 'lu-underline'],
    enabled: true,
    minWordLength: 2,
    maxWordLength: 30
  });

  // 2. 初始化Tooltip管理器
  const tooltipManager = initializeTooltips({
    theme: 'dark'
  });

  // 3. 初始化Slider管理器
  const sliderManager = initializeSlider();

  // 4. 设置交互事件处理器
  const interactionHandlers = setupInteractionHandlers({
    highlightManager,
    tooltipManager,
    sliderManager
  });

  // 5. 初始化调试功能（仅开发环境）
  // debugFunctions 初始化已移除

  return {
    highlightManager,
    tooltipManager,
    sliderManager,
    interactionHandlers,
    // debugFunctions 返回已移除
  };
}