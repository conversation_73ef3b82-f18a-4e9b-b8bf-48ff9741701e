/**
 * Translation State Manager
 * 翻译状态管理器 - 原子化管理翻译流程的所有状态
 */

import { 
  TranslationState, 
  TranslationStats, 
  TranslationEventType,
  StateChangeEventData,
  ProgressEventData
} from './types';
import { TranslationEventBus } from './event-bus';
// debug 导入已移除，使用手动 console.log

/**
 * 状态管理器配置
 */
export interface StateManagerConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 初始状态 */
  initialState?: TranslationState;
  /** 事件总线 */
  eventBus?: TranslationEventBus;
}

/**
 * 翻译状态管理器
 * 
 * 职责：
 * 1. 管理翻译流程的状态转换
 * 2. 跟踪正在处理和已完成的元素
 * 3. 维护翻译统计信息
 * 4. 发布状态变化事件
 */
export class StateManager {
  private currentState: TranslationState;
  private stats: TranslationStats;
  private logger = console;
  private eventBus?: TranslationEventBus;
  private debugMode: boolean;

  // 元素状态跟踪
  private processingElements = new WeakSet<HTMLElement>();
  private translatedElements = new WeakSet<HTMLElement>();
  private failedElements = new WeakSet<HTMLElement>();

  // 状态变更历史
  private stateHistory: Array<{
    state: TranslationState;
    timestamp: number;
    reason?: string;
  }> = [];

  constructor(config: StateManagerConfig = {}) {
    this.debugMode = config.debug ?? false;
    this.currentState = config.initialState ?? TranslationState.IDLE;
    this.eventBus = config.eventBus;
    
    // 初始化统计信息
    this.stats = this.initializeStats();
    
    // 记录初始状态
    this.stateHistory.push({
      state: this.currentState,
      timestamp: Date.now(),
      reason: 'initialization'
    });

    if (this.debugMode) {
      this.logger.info('StateManager initialized', {
        initialState: this.currentState
      });
    }
  }

  /**
   * 获取当前状态
   */
  getState(): TranslationState {
    return this.currentState;
  }

  /**
   * 设置状态
   * @param newState 新状态
   * @param reason 变更原因
   */
  setState(newState: TranslationState, reason?: string): void {
    if (newState === this.currentState) {
      return; // 状态未变化
    }

    const previousState = this.currentState;
    this.currentState = newState;

    // 记录状态历史
    this.stateHistory.push({
      state: newState,
      timestamp: Date.now(),
      reason
    });

    // 限制历史记录长度
    if (this.stateHistory.length > 50) {
      this.stateHistory = this.stateHistory.slice(-50);
    }

    if (this.debugMode) {
      this.logger.info('State changed', {
        from: previousState,
        to: newState,
        reason,
        timestamp: Date.now()
      });
    }

    // 发布状态变更事件
    if (this.eventBus) {
      const eventData: StateChangeEventData = {
        previousState,
        currentState: newState,
        reason
      };
      this.eventBus.publish(TranslationEventType.STATE_CHANGED, eventData, 'StateManager');
    }
  }

  /**
   * 检查是否可以开始翻译
   */
  canStart(): boolean {
    return this.currentState === TranslationState.IDLE;
  }

  /**
   * 检查是否正在运行
   */
  isRunning(): boolean {
    return [
      TranslationState.SCANNING,
      TranslationState.TRANSLATING,
      TranslationState.RENDERING
    ].includes(this.currentState);
  }

  /**
   * 检查是否已完成
   */
  isCompleted(): boolean {
    return this.currentState === TranslationState.COMPLETED;
  }

  /**
   * 检查是否出错
   */
  hasError(): boolean {
    return this.currentState === TranslationState.ERROR;
  }

  /**
   * 标记元素为正在处理状态
   * @param element 要标记的HTML元素
   * @returns 如果成功标记返回true，否则返回false
   */
  markAsProcessing(element: HTMLElement): boolean {
    // 检查元素是否已在其他状态中
    if (this.processingElements.has(element) || 
        this.translatedElements.has(element) || 
        this.failedElements.has(element)) {
      if (this.debugMode) {
        this.logger.info('Element already in processing/translated/failed state', {
          tagName: element.tagName,
          hasProcessing: this.processingElements.has(element),
          hasTranslated: this.translatedElements.has(element),
          hasFailed: this.failedElements.has(element)
        });
      }
      return false;
    }

    // 检查DOM属性
    if (element.getAttribute('data-lu-translated') === 'true' ||
        element.getAttribute('data-lu-processing') === 'true') {
      if (this.debugMode) {
        this.logger.info('Element has existing translation DOM attributes', {
          tagName: element.tagName
        });
      }
      return false;
    }

    // 检查是否存在翻译wrapper
    if (element.querySelector('.lu-wrapper') || element.closest('.lu-wrapper')) {
      if (this.debugMode) {
        this.logger.info('Element has existing translation wrapper', {
          tagName: element.tagName
        });
      }
      return false;
    }

    // 标记为正在处理
    this.processingElements.add(element);
    element.setAttribute('data-lu-processing', 'true');

    if (this.debugMode) {
      this.logger.info('Element marked as processing', {
        tagName: element.tagName,
        textPreview: element.textContent?.slice(0, 50)
      });
    }

    return true;
  }

  /**
   * 标记元素为已翻译状态
   * @param element 要标记的HTML元素
   */
  markAsTranslated(element: HTMLElement): void {
    // 从正在处理状态移除
    this.processingElements.delete(element);
    element.removeAttribute('data-lu-processing');

    // 标记为已翻译
    this.translatedElements.add(element);
    element.setAttribute('data-lu-translated', 'true');

    // 更新统计
    this.stats.translation.successfulRequests++;

    if (this.debugMode) {
      this.logger.info('Element marked as translated', {
        tagName: element.tagName,
        textPreview: element.textContent?.slice(0, 50)
      });
    }
  }

  /**
   * 标记元素为失败状态
   * @param element 要标记的HTML元素
   * @param error 错误信息
   */
  markAsFailed(element: HTMLElement, error?: string): void {
    // 从正在处理状态移除
    this.processingElements.delete(element);
    element.removeAttribute('data-lu-processing');

    // 标记为失败
    this.failedElements.add(element);
    element.setAttribute('data-lu-failed', 'true');
    if (error) {
      element.setAttribute('data-lu-error', error);
    }

    // 更新统计
    this.stats.translation.failedRequests++;

    if (this.debugMode) {
      this.logger.warn('Element marked as failed', {
        tagName: element.tagName,
        textPreview: element.textContent?.slice(0, 50),
        error
      });
    }
  }

  /**
   * 检查元素是否可用于处理
   * @param element 要检查的HTML元素
   * @returns 如果可用返回true，否则返回false
   */
  isElementAvailable(element: HTMLElement): boolean {
    // 🔧 修复：允许正在处理中的元素进行渲染操作
    // 正在处理中的元素应该可以继续到渲染阶段
    
    // 检查是否已完成翻译或失败
    if (this.translatedElements.has(element) || 
        this.failedElements.has(element)) {
      return false;
    }

    // 检查DOM属性 - 已翻译或失败的不可用
    if (element.getAttribute('data-lu-translated') === 'true' ||
        element.getAttribute('data-lu-failed') === 'true') {
      return false;
    }

    // 检查wrapper元素 - 已有翻译结构的不可用
    if (element.querySelector('.lu-wrapper, .lu-block, .lu-inline') ||
        element.closest('.lu-wrapper, .lu-block, .lu-inline') ||
        element.classList.contains('lu-wrapper') ||
        element.classList.contains('lu-block') ||
        element.classList.contains('lu-inline')) {
      return false;
    }

    // 🔧 修复：基础DOM可用性检查
    if (!element.isConnected || !element.parentElement) {
      if (this.debugMode) {
        this.logger.warn('Element not connected to DOM', {
          tagName: element.tagName,
          isConnected: element.isConnected,
          hasParent: !!element.parentElement
        });
      }
      return false;
    }

    return true;
  }

  /**
   * 检查元素是否已存在翻译
   * @param element 要检查的HTML元素
   * @returns 如果存在翻译返回true，否则返回false
   */
  hasExistingTranslation(element: HTMLElement): boolean {
    return this.translatedElements.has(element) ||
           element.getAttribute('data-lu-translated') === 'true' ||
           !!element.querySelector('.lu-wrapper, .lu-block, .lu-inline') ||
           !!element.closest('.lu-wrapper, .lu-block, .lu-inline');
  }

  /**
   * 获取统计信息
   */
  getStats(): TranslationStats {
    return { ...this.stats }; // 返回副本
  }

  /**
   * 更新扫描统计
   * @param totalNodes 总节点数
   * @param translatableNodes 可翻译节点数
   */
  updateScanStats(totalNodes: number, translatableNodes: number): void {
    this.stats.scan.totalNodes = totalNodes;
    this.stats.scan.translatableNodes = translatableNodes;
    this.stats.scan.processedNodes = translatableNodes; // 假设所有可翻译节点都会被处理
  }

  /**
   * 更新翻译统计
   * @param totalRequests 总请求数
   */
  updateTranslationStats(totalRequests: number): void {
    this.stats.translation.totalRequests = totalRequests;
  }

  /**
   * 更新渲染统计
   * @param totalRenders 总渲染数
   * @param successfulRenders 成功渲染数
   * @param failedRenders 失败渲染数
   */
  updateRenderStats(totalRenders: number, successfulRenders: number, failedRenders: number): void {
    this.stats.render.totalRenders = totalRenders;
    this.stats.render.successfulRenders = successfulRenders;
    this.stats.render.failedRenders = failedRenders;
  }

  /**
   * 报告进度
   * @param processed 已处理数量
   * @param total 总数量
   * @param successful 成功数量
   * @param failed 失败数量
   * @param currentText 当前处理的文本
   */
  reportProgress(
    processed: number, 
    total: number, 
    successful: number, 
    failed: number,
    currentText?: string
  ): void {
    const progressData: ProgressEventData = {
      processed,
      total,
      successful,
      failed,
      currentText,
      percentage: total > 0 ? (processed / total) * 100 : 0
    };

    if (this.eventBus) {
      this.eventBus.publish(TranslationEventType.PROGRESS_UPDATED, progressData, 'StateManager');
    }

    if (this.debugMode) {
      this.logger.info('Progress updated', progressData);
    }
  }

  /**
   * 重置状态和统计
   */
  reset(): void {
    // 重置状态
    this.setState(TranslationState.IDLE, 'reset');

    // 重置统计
    this.stats = this.initializeStats();

    // 清理元素状态
    this.cleanupElementStates();

    // 清理状态历史
    this.stateHistory = [{
      state: TranslationState.IDLE,
      timestamp: Date.now(),
      reason: 'reset'
    }];

    if (this.debugMode) {
      this.logger.info('StateManager reset');
    }
  }

  /**
   * 清理所有元素状态
   */
  cleanupElementStates(): void {
    try {
      // 清理DOM属性
      const processedElements = document.querySelectorAll(
        '[data-lu-processing], [data-lu-translated], [data-lu-failed], [data-lu-error]'
      );
      
      processedElements.forEach(element => {
        if (element instanceof HTMLElement) {
          element.removeAttribute('data-lu-processing');
          element.removeAttribute('data-lu-translated');
          element.removeAttribute('data-lu-failed');
          element.removeAttribute('data-lu-error');
        }
      });

      // 重置WeakSet
      this.processingElements = new WeakSet<HTMLElement>();
      this.translatedElements = new WeakSet<HTMLElement>();
      this.failedElements = new WeakSet<HTMLElement>();

      if (this.debugMode) {
        this.logger.info(`Cleaned up ${processedElements.length} element states`);
      }
    } catch (error) {
      this.logger.error('Error during element states cleanup:', error);
    }
  }

  /**
   * 获取状态历史
   * @param limit 限制数量
   */
  getStateHistory(limit?: number): Array<{
    state: TranslationState;
    timestamp: number;
    reason?: string;
  }> {
    const history = [...this.stateHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * 销毁状态管理器
   */
  destroy(): void {
    this.cleanupElementStates();
    this.stateHistory = [];
    
    if (this.debugMode) {
      this.logger.info('StateManager destroyed');
    }
  }

  /**
   * 初始化统计信息
   * @private
   */
  private initializeStats(): TranslationStats {
    return {
      scan: {
        totalNodes: 0,
        translatableNodes: 0,
        processedNodes: 0
      },
      translation: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageTime: 0
      },
      render: {
        totalRenders: 0,
        successfulRenders: 0,
        failedRenders: 0
      }
    };
  }
}