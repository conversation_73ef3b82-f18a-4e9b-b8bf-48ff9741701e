/**
 * DOM Scanner
 * DOM扫描器 - 负责扫描、分析、过滤和排序DOM节点
 */

import { ScannedNode, ScanConfig, ViewportBounds, ViewportScanResult } from './types';
import { TaskPriority } from '../../utils/promise-pool';
import { DomElementAnalyzer, TranslatableElement, AnalysisStats } from '../../core/dom-element-analyzer';
// debug 导入已移除，使用手动 console.log
import { getConfigLoader } from '../../core/config-loader';

/**
 * DOM扫描器
 * 
 * 职责：
 * 1. 扫描DOM树，识别可翻译节点
 * 2. 分析节点内容和结构
 * 3. 过滤掉冲突和重复的节点
 * 4. 为节点分配优先级
 */
export class DomScanner {
  private elementAnalyzer: DomElementAnalyzer;
  private logger = console;
  private debugMode: boolean;

  constructor(config: { debug?: boolean } = {}) {
    // 🔧 优化：统一调试日志系统 - 优先使用全局配置
    const globalConfig = getConfigLoader().getSettings();
    this.debugMode = config.debug ?? globalConfig.debugMode ?? false;

    this.elementAnalyzer = new DomElementAnalyzer();

    if (this.debugMode) {
      this.logger.info('DomScanner initialized with new DOM element analyzer', {
        explicitDebug: config.debug,
        globalDebugMode: globalConfig.debugMode,
        finalDebugMode: this.debugMode
      });
    }
  }

  /**
   * 扫描指定根节点下的所有可翻译节点
   * @param config 扫描配置
   * @returns 扫描结果
   */
  scan(config: ScanConfig = {}): {
    nodes: ScannedNode[];
    stats: {
      totalScanned: number;
      translatableFound: number;
      filtered: number;
      finalCount: number;
      duration: number;
    };
  } {
    const startTime = performance.now();
    const rootNode = config.rootNode || document.body;

    if (this.debugMode) {
      this.logger.info('Starting DOM scan', {
        rootNode: rootNode.tagName,
        excludeSelectors: config.excludeSelectors,
        minTextLength: config.minTextLength,
        maxDepth: config.maxDepth
      });
    }

    // 使用新的DomElementAnalyzer进行基础分析
    const analysisResult = this.elementAnalyzer.analyzeElements(rootNode);

    // 转换为新的ScannedNode格式
    let scannedNodes = analysisResult.elements.map((element, index) =>
      this.convertToScannedNode(element, index)
    );

    const totalScanned = analysisResult.stats?.totalScanned || 0;
    const translatableFound = scannedNodes.length;

    // 应用额外的过滤规则
    scannedNodes = this.applyFilters(scannedNodes, config);

    // 过滤父子冲突
    const filteredNodes = this.filterParentChildConflicts(scannedNodes);

    // 按优先级排序
    const prioritizedNodes = this.prioritizeNodes(filteredNodes);

    const duration = performance.now() - startTime;
    const stats = {
      totalScanned,
      translatableFound,
      filtered: translatableFound - filteredNodes.length,
      finalCount: prioritizedNodes.length,
      duration
    };

    if (this.debugMode) {
      this.logger.info('DOM scan completed', {
        ...stats,
        duration: `${duration.toFixed(2)}ms`
      });
    }

    return {
      nodes: prioritizedNodes,
      stats
    };
  }

  /**
   * 分析单个节点
   * @param element HTML元素
   * @param position 位置索引
   * @returns 分析结果
   */
  analyzeNode(element: HTMLElement, position: number): ScannedNode | null {
    try {
      // 使用新的DomElementAnalyzer分析元素
      const elementInfo = this.elementAnalyzer.analyzeElement(element, position);
      if (!elementInfo) {
        return null;
      }

      return this.convertToScannedNode(elementInfo, position);
    } catch (error) {
      if (this.debugMode) {
        this.logger.warn('Error analyzing element:', {
          element: element.tagName,
          error: (error as Error).message
        });
      }
      return null;
    }
  }

  /**
   * 将TranslatableElement转换为ScannedNode
   * @private
   */
  private convertToScannedNode(translatable: TranslatableElement, position: number): ScannedNode {
    const element = translatable.element;
    const rect = element.getBoundingClientRect();
    const isVisible = rect.top >= 0 && rect.top <= window.innerHeight;

    return {
      element: translatable.element,
      text: translatable.text,
      htmlContent: translatable.htmlContent,
      hasHtmlStructure: translatable.hasHtmlStructure,
      priority: this.calculatePriority(translatable, isVisible),
      position,
      links: translatable.links,
      metadata: {
        tagName: element.tagName.toLowerCase(),
        className: element.className || undefined,
        textLength: translatable.text.length,
        isVisible
      }
    };
  }

  /**
   * 计算元素优先级
   * @private
   */
  private calculatePriority(translatable: TranslatableElement, isVisible: boolean): TaskPriority {
    const element = translatable.element;
    const tagName = element.tagName.toLowerCase();
    const textLength = translatable.text.length;

    // 标题元素最高优先级
    if (['h1', 'h2', 'h3'].includes(tagName)) {
      return TaskPriority.CRITICAL;
    }

    // 可见元素优先级更高
    if (isVisible) {
      return TaskPriority.HIGH;
    }

    // 短文本优先处理
    if (textLength < 100) {
      return TaskPriority.HIGH;
    }

    // 其他重要元素
    if (['h4', 'h5', 'h6', 'strong', 'em', 'b', 'i'].includes(tagName)) {
      return TaskPriority.HIGH;
    }

    return TaskPriority.NORMAL;
  }

  /**
   * 应用过滤规则
   * @private
   */
  private applyFilters(nodes: ScannedNode[], config: ScanConfig): ScannedNode[] {
    let filteredNodes = [...nodes];

    // 应用排除选择器
    if (config.excludeSelectors && config.excludeSelectors.length > 0) {
      filteredNodes = filteredNodes.filter(node => {
        return !config.excludeSelectors!.some(selector => {
          try {
            return node.element.matches(selector) ||
              node.element.closest(selector);
          } catch {
            return false;
          }
        });
      });
    }

    // 应用最小文本长度过滤
    if (config.minTextLength && config.minTextLength > 0) {
      filteredNodes = filteredNodes.filter(node =>
        node.text.length >= config.minTextLength!
      );
    }

    // 应用最大深度过滤
    if (config.maxDepth && config.maxDepth > 0) {
      const rootNode = config.rootNode || document.body;
      filteredNodes = filteredNodes.filter(node => {
        const depth = this.getElementDepth(node.element, rootNode);
        return depth <= config.maxDepth!;
      });
    }

    return filteredNodes;
  }

  /**
   * 过滤父子冲突的节点
   * @private
   */
  /**
   * 过滤父子冲突的节点（增强版）
   * 🚀 优化策略：优先保留子节点，减少冗余翻译
   * @private
   */
  private filterParentChildConflicts(nodes: ScannedNode[]): ScannedNode[] {
    if (nodes.length < 2) {
      return nodes;
    }

    if (this.debugMode) {
      this.logger.info(`🚀 开始父子冲突过滤：${nodes.length} 个节点（优先子节点策略）`);
    }

    const elementToNodeMap = new Map<HTMLElement, ScannedNode>();
    nodes.forEach(node => elementToNodeMap.set(node.element, node));

    const nodesToRemove = new Set<ScannedNode>();

    for (const node of nodes) {
      if (nodesToRemove.has(node)) {
        continue;
      }

      const childElement = node.element;
      const parentElement = childElement.parentElement;

      // 检查是否存在一个父节点，该父节点也在待翻译列表中
      if (parentElement && elementToNodeMap.has(parentElement)) {
        const parentNode = elementToNodeMap.get(parentElement)!;

        // 🚀 新策略：优先保留子节点，减少重复翻译
        // 获取并清理文本内容
        const childText = (childElement.textContent || '').trim();
        const parentText = (parentElement.textContent || '').trim();
        
        // 避免除以零
        if (parentText.length === 0) continue;

        // 计算子节点文本在父节点中的占比
        const overlapRatio = childText.length / parentText.length;
        
        // 🚀 调整阈值：降低保留父节点的门槛，更多情况下保留子节点
        const CHILD_PRIORITY_THRESHOLD = 0.7; // 从0.9降至0.7，更倾向于保留子节点

        if (overlapRatio > CHILD_PRIORITY_THRESHOLD) {
          // 🚀 优先保留子节点策略：当子节点内容占父节点70%以上时，移除父节点
          nodesToRemove.add(parentNode);
          if (this.debugMode) {
            this.logger.info(`🎯 父子冲突解决：高占比 (${(overlapRatio * 100).toFixed(1)}%)，保留子节点`, {
              removedParent: `${parentNode.element.tagName}.${parentNode.element.className || ''}`,
              keptChild: `${childElement.tagName}.${childElement.className || ''}`,
              childText: childText.slice(0, 50),
              parentText: parentText.slice(0, 50)
            });
          }
        } else {
          // 🚀 检查额外的子节点优先条件
          const shouldPrioritizeChild = this.shouldPrioritizeChild(childElement, parentElement);
          
          if (shouldPrioritizeChild) {
            // 即使占比不高，但子节点更具体，也保留子节点
            nodesToRemove.add(parentNode);
            if (this.debugMode) {
              this.logger.info(`🎯 父子冲突解决：子节点更具体，保留子节点`, {
                removedParent: `${parentNode.element.tagName}.${parentNode.element.className || ''}`,
                keptChild: `${childElement.tagName}.${childElement.className || ''}`,
                reason: '子节点标签更具体'
              });
            }
          } else {
            // 保留父节点的情况（现在更少）
            nodesToRemove.add(node);
            if (this.debugMode) {
              this.logger.info(`🎯 父子冲突解决：低占比 (${(overlapRatio * 100).toFixed(1)}%)，保留父节点上下文`, {
                keptParent: `${parentNode.element.tagName}.${parentNode.element.className || ''}`,
                removedChild: `${childElement.tagName}.${childElement.className || ''}`,
                reason: '父节点包含重要上下文'
              });
            }
          }
        }
      }
    }

    if (nodesToRemove.size > 0) {
      const filteredNodes = nodes.filter(node => !nodesToRemove.has(node));
      if (this.debugMode) {
        this.logger.info(
          `🎉 父子冲突过滤完成：移除 ${nodesToRemove.size} 个节点，保留 ${filteredNodes.length} 个节点（子节点优先策略）`
        );
      }
      return filteredNodes;
    }

    return nodes;
  }

  /**
   * 🚀 判断是否应该优先保留子节点
   * @private
   */
  private shouldPrioritizeChild(childElement: HTMLElement, parentElement: HTMLElement): boolean {
    const childTag = childElement.tagName.toLowerCase();
    const parentTag = parentElement.tagName.toLowerCase();

    // --- BEGIN FIX ---
    // 如果父元素是段落(p)，而子元素是链接(a)，则优先保留父元素以确保段落完整性。
    if (parentTag === 'p' && childTag === 'a') {
        return false;
    }
    // --- END FIX ---
    
    // 🚀 具体标签优先级：更具体的语义标签优先于通用容器
    const specificTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'a', 'span', 'strong', 'em', 'b', 'i'];
    const containerTags = ['div', 'section', 'article', 'main', 'aside', 'header', 'footer'];
    
    // 如果子元素是具体标签，父元素是容器标签，优先保留子元素
    if (specificTags.includes(childTag) && containerTags.includes(parentTag)) {
      return true;
    }
    
    // 🚀 标题元素始终优先
    if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(childTag)) {
      return true;
    }
    
    // 🚀 链接和强调元素优先于段落
    if (['a', 'strong', 'em', 'b', 'i'].includes(childTag) && parentTag === 'p') {
      return true;
    }
    
    return false;
  }

  /**
   * 调试特定元素的处理情况
   * 🔧 专门用于调试 "Game on" 等可能被遗漏的元素
   * @private
   */
  private debugSpecificElements(finalNodes: ScannedNode[]): void {
    // 查找页面中所有的 H2 元素
    const allH2Elements = document.querySelectorAll('h2');
    const finalH2Elements = finalNodes.filter(node => node.element.tagName.toLowerCase() === 'h2');

    this.logger.info(`H2 elements debug`, {
      totalH2InPage: allH2Elements.length,
      finalH2InNodes: finalH2Elements.length,
      h2Texts: Array.from(allH2Elements).map(h2 => h2.textContent?.trim()),
      finalH2Texts: finalH2Elements.map(node => node.text)
    });

    // 特别检查 "Game on" 元素
    const gameOnElements = Array.from(allH2Elements).filter(h2 =>
      h2.textContent?.trim() === 'Game on'
    );

    if (gameOnElements.length > 0) {
      const gameOnElement = gameOnElements[0] as HTMLElement;
      const isInFinalNodes = finalNodes.some(node => node.element === gameOnElement);

      // 🔧 增强调试：逐步检查元素在各个阶段的状态
      const debugInfo = this.traceElementProcessing(gameOnElement);

      this.logger.info(`"Game on" element debug`, {
        found: true,
        tagName: gameOnElement.tagName,
        className: gameOnElement.className,
        textContent: gameOnElement.textContent,
        isInFinalNodes,
        parentElement: {
          tagName: gameOnElement.parentElement?.tagName,
          className: gameOnElement.parentElement?.className,
          textContent: gameOnElement.parentElement?.textContent?.slice(0, 50)
        },
        processingTrace: debugInfo
      });

      // 尝试分析这个元素为什么没有被包含
      if (!isInFinalNodes) {
        const analysisResult = this.analyzeNode(gameOnElement, -1);
        this.logger.info(`"Game on" analysis result`, {
          canAnalyze: !!analysisResult,
          analysisResult: analysisResult ? {
            text: analysisResult.text,
            priority: analysisResult.priority,
            tagName: analysisResult.metadata?.tagName || gameOnElement.tagName
          } : null
        });
      }
    } else {
      this.logger.info(`"Game on" element not found in page`);
    }
  }

  /**
   * 追踪元素在整个处理过程中的状态
   * @private
   */
  private traceElementProcessing(element: HTMLElement): any {
    // 检查是否可以被分析
    const analysisResult = this.elementAnalyzer.analyzeElement(element, -1);
    
    // 检查父元素情况
    const parentElement = element.parentElement;
    const parentAnalysisResult = parentElement ? this.elementAnalyzer.analyzeElement(parentElement, -1) : null;
    
    const parentInfo = parentElement ? {
      tagName: parentElement.tagName,
      className: parentElement.className,
      canBeAnalyzed: !!parentAnalysisResult,
      textLength: parentElement.textContent?.length || 0,
      textPreview: parentElement.textContent?.slice(0, 100) || ''
    } : null;

    // 检查是否存在父子冲突的可能性
    let potentialConflict = false;
    if (parentElement && analysisResult && parentAnalysisResult) {
      const parentText = parentElement.textContent?.trim() || '';
      const childText = element.textContent?.trim() || '';
      
      if (parentText.includes(childText)) {
        const overlapRatio = childText.length / parentText.length;
        potentialConflict = overlapRatio > 0.5;
      }
    }

    return {
      canBeAnalyzed: !!analysisResult,
      hasParent: !!parentElement,
      parentInfo: parentInfo,
      potentialParentChildConflict: potentialConflict,
      elementText: element.textContent?.trim() || '',
      elementTextLength: element.textContent?.length || 0
    };
  }

  /**
   * 查找被排除父元素中遗漏的可翻译子节点
   * 🔧 关键修复：确保H2等原子内容块不会因父元素被排除而遗漏
   * @private
   */
  private findMissingChildNodes(
    excludedParent: HTMLElement,
    existingNodeElements: Set<HTMLElement>,
    missingChildNodes: ScannedNode[]
  ): void {
    // 定义原子内容块选择器（这些元素应该被单独翻译）
    const atomicSelectors = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'li', 'blockquote', 'figcaption'];

    for (const selector of atomicSelectors) {
      // 🔧 修复：查找所有后代元素，不仅仅是直接子元素
      const childElements = excludedParent.querySelectorAll(selector);

      for (const childElement of childElements) {
        const htmlChild = childElement as HTMLElement;

        // 如果这个子元素不在现有节点列表中，尝试将其添加
        if (!existingNodeElements.has(htmlChild)) {
          const childNode = this.analyzeNode(htmlChild, missingChildNodes.length);

          if (childNode) {
            missingChildNodes.push(childNode);
            existingNodeElements.add(htmlChild); // 防止重复添加

            if (this.debugMode) {
              this.logger.info(`Recovered missing child node`, {
                childTag: htmlChild.tagName,
                childClass: htmlChild.className,
                childText: htmlChild.textContent?.trim().slice(0, 30),
                reason: 'parent-excluded-recovery',
                parentTag: excludedParent.tagName,
                parentClass: excludedParent.className
              });
            }
          }
        }
      }
    }
  }

  /**
   * 智能判断父子元素是否存在真正的翻译冲突
   * @private
   */
  private shouldTreatAsConflict(
    parentElement: HTMLElement,
    childElement: HTMLElement,
    parentText: string,
    childText: string
  ): boolean {
    // 如果任一文本为空，不认为是冲突
    if (!parentText || !childText) {
      return false;
    }

    // 计算文本重叠率
    const parentTrimmed = parentText.trim();
    const childTrimmed = childText.trim();

    // 如果子元素的文本完全包含在父元素中，计算重叠比例
    let overlapRatio = 0;
    if (parentTrimmed.includes(childTrimmed)) {
      overlapRatio = childTrimmed.length / parentTrimmed.length;
    } else if (childTrimmed.includes(parentTrimmed)) {
      overlapRatio = parentTrimmed.length / childTrimmed.length;
    } else {
      // 没有包含关系，不认为是冲突
      return false;
    }

    const parentTag = parentElement.tagName.toLowerCase();
    const childTag = childElement.tagName.toLowerCase();

    const isParentContainerType = ['div', 'section', 'article', 'main', 'aside'].includes(parentTag);
    const isChildAtomicBlock = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'li', 'blockquote'].includes(childTag);
    
    // 🔧 关键修复：链接元素（a标签）应该被优先选择
    const isChildLinkElement = childTag === 'a';

    // 🔧 关键修复：对于链接元素，使用更宽松的冲突判断
    if (isChildLinkElement) {
      // 链接元素通常包含具体的可翻译内容，应该被优先选择
      // 只有在重叠率极高（几乎完全相同）时才不认为是冲突
      if (overlapRatio > 0.90) {
        return true; // 内容几乎完全相同，可以排除父元素
      }
      
      // 对于链接元素，即使有中等重叠率也优先选择链接
      // 这是因为链接通常包含更具体的语义内容
      return overlapRatio > 0.95; // 提高阈值，更倾向于保留链接
    }

    // 🔧 关键修复：对于原子内容块，使用更严格的冲突判断
    if (isChildAtomicBlock) {
      // 对于标题元素（H1-H6），只有在重叠率极高时才认为是冲突
      if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(childTag)) {
        // 标题通常是独立的翻译单元，只有在几乎完全重叠时才排除父元素
        return overlapRatio > 0.90;
      }
      
      // 🔧 特殊处理：li 元素包含链接的情况
      if (childTag === 'li') {
        // 如果 li 包含链接元素，降低冲突阈值，让链接元素优先
        const hasDirectLinkChild = childElement.querySelector(':scope > a');
        if (hasDirectLinkChild) {
          return overlapRatio > 0.85; // 更容易被排除，让链接元素被选中
        }
      }
      
      // 对于其他原子块（p, li等），使用中等严格度
      return overlapRatio > 0.75;
    }

    // 🎯 智能冲突判断规则：
    // 1. 如果重叠率 > 85%，认为是冲突（内容基本相同）
    // 2. 如果父元素是纯容器（div, section等）且重叠率 > 70%，认为是冲突

    // 高重叠率 - 明显冲突
    if (overlapRatio > 0.85) {
      return true;
    }

    // 容器父元素 + 高重叠率
    if (isParentContainerType && overlapRatio > 0.70) {
      return true;
    }

    // 其他情况不认为是冲突，允许共存
    return false;
  }

  /**
   * 按优先级排序节点
   * @private
   */
  private prioritizeNodes(nodes: ScannedNode[]): ScannedNode[] {
    return nodes.sort((a, b) => {
      // 先按优先级排序（数字越小优先级越高）
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }

      // 然后按位置排序
      return a.position - b.position;
    });
  }

  /**
   * 获取元素深度
   * @private
   */
  private getElementDepth(element: HTMLElement, rootNode: HTMLElement): number {
    let depth = 0;
    let current = element.parentElement;

    while (current && current !== rootNode) {
      depth++;
      current = current.parentElement;
    }

    return depth;
  }

  /**
   * 异步分块扫描方法
   * 🚀 性能优化：将DOM遍历分块处理，避免长时间阻塞主线程
   * @param config 扫描配置
   * @param chunkSize 每个分块的处理节点数量，默认200
   * @returns Promise<ScannedNode[]> 扫描结果
   */
  async scanAsync(
    config: ScanConfig = {},
    chunkSize: number = 200
  ): Promise<ScannedNode[]> {
    const startTime = performance.now();
    const rootNode = config.rootNode || document.body;

    if (this.debugMode) {
      this.logger.info('Starting async DOM scan', {
        rootNode: rootNode.tagName,
        chunkSize,
        excludeSelectors: config.excludeSelectors,
        minTextLength: config.minTextLength,
        maxDepth: config.maxDepth
      });
    }

    const results: ScannedNode[] = [];

    // 使用 TreeWalker 创建遍历器
    const walker = this.createTreeWalker(rootNode);

    let processedInChunk = 0;
    let totalProcessed = 0;

    // 分块处理DOM节点
    while (true) {
      const chunk = await this.processChunkAsync(walker, chunkSize);
      if (chunk.length === 0) break;
      
      results.push(...chunk);
      totalProcessed += chunk.length;
      
      if (this.debugMode && totalProcessed % (chunkSize * 2) === 0) {
        this.logger.info(`Async scan progress: ${totalProcessed} nodes processed`);
      }

      // 让出执行权，避免阻塞UI
      await this.yieldToMain();
    }

    // 应用过滤和排序
    const filteredResults = this.applyFiltersAndSort(results, config);
    
    const duration = performance.now() - startTime;
    
    if (this.debugMode) {
      this.logger.info('Async DOM scan completed', {
        totalFound: results.length,
        finalCount: filteredResults.length,
        duration: `${duration.toFixed(2)}ms`,
        performance: `${(filteredResults.length / duration * 1000).toFixed(0)} nodes/sec`
      });
    }

    return filteredResults;
  }

  /**
   * 创建 TreeWalker 实例 - 🚀 性能优化版本：增强预过滤，预编译正则表达式
   * @private
   */
  private createTreeWalker(rootNode: HTMLElement): TreeWalker {
    // 🚀 预编译正则表达式，避免重复创建
    const SYMBOL_ONLY_REGEX = /^[\d\s\-_.,;:!?()[\]{}'"`~@#$%^&*+=<>/\\|]*$/;
    
    // 🚀 预编译跳过的标签和类名集合，使用Set提升查找性能
    const SKIP_TAGS_SET = new Set([
      'script', 'style', 'noscript', 'meta', 'link', 'head', 'title',
      'canvas', 'svg', 'iframe', 'embed', 'object', 'audio', 'video',
      'input', 'select', 'textarea', 'button', 'form', 'fieldset', 'legend',
      'br', 'hr', 'img', 'area', 'base', 'col', 'source', 'track', 'wbr'
    ]);
    
    const SKIP_CLASSES_SET = new Set([
      'notranslate', 'no-translate', 'skiptranslate',
      'lu-wrapper', 'lu-block', 'lu-inline', 'lu-strategy', 'lu-trans',
      'translation-result', 'translated-content',
      'code', 'hljs', 'highlight', 'syntax-highlight',
      'icon', 'emoji', 'symbol', 'badge', 'tag'
    ]);

    return document.createTreeWalker(
      rootNode,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node: Node) => {
          const element = node as HTMLElement;
          
          // 🚀 优化1：使用Set快速标签名过滤
          const tagName = element.tagName.toLowerCase();
          if (SKIP_TAGS_SET.has(tagName)) {
            return NodeFilter.FILTER_REJECT;
          }
          
          // 🚀 优化2：早期CSS过滤 - 避免不必要的样式计算
          // 检查基本的隐藏属性，避免昂贵的 getComputedStyle 调用
          if (element.hasAttribute('hidden') || 
              element.getAttribute('aria-hidden') === 'true' ||
              element.style.display === 'none' ||
              element.style.visibility === 'hidden') {
            return NodeFilter.FILTER_REJECT;
          }
          
          // 🚀 优化3：使用缓存的类名检查，避免重复字符串操作
          const className = element.className;
          if (className) {
            // 快速检查：使用some而非循环，一旦找到匹配立即返回
            for (const skipClass of SKIP_CLASSES_SET) {
              if (className.includes(skipClass)) {
                return NodeFilter.FILTER_REJECT;
              }
            }
          }
          
          // 🚀 优化4：一次性获取文本内容，避免重复DOM访问
          const textContent = element.textContent;
          if (!textContent) {
            return NodeFilter.FILTER_REJECT;
          }
          
          const trimmedText = textContent.trim();
          
          // 🚀 优化5：快速长度检查
          if (trimmedText.length < 2) {
            return NodeFilter.FILTER_REJECT;
          }
          
          // 🚀 优化6：使用预编译的正则表达式
          if (SYMBOL_ONLY_REGEX.test(trimmedText)) {
            return NodeFilter.FILTER_REJECT;
          }
          
          // 🚀 优化7：仅在必要时进行昂贵的样式计算
          try {
            const style = window.getComputedStyle(element);
            if (style.display === 'none' || style.visibility === 'hidden') {
              return NodeFilter.FILTER_REJECT;
            }
          } catch (error) {
            // 如果样式计算失败，为安全起见保留元素
            if (this.debugMode) {
              console.warn('样式计算失败，保留元素', { tagName, error });
            }
          }
          
          return NodeFilter.FILTER_ACCEPT;
        }
      }
    );
  }

  /**
   * 处理一个分块的节点
   * @private
   */
  private async processChunkAsync(
    walker: TreeWalker,
    chunkSize: number
  ): Promise<ScannedNode[]> {
    const chunk: ScannedNode[] = [];
    let processed = 0;

    while (processed < chunkSize && walker.nextNode()) {
      const element = walker.currentNode as HTMLElement;
      
      // 使用现有的 analyzeNode 方法分析元素
      const scannedNode = this.analyzeNode(element, chunk.length);
      if (scannedNode) {
        chunk.push(scannedNode);
      }
      
      processed++;
    }

    return chunk;
  }

  /**
   * 应用过滤规则和排序
   * @private
   */
  private applyFiltersAndSort(nodes: ScannedNode[], config: ScanConfig): ScannedNode[] {
    // 应用额外的过滤规则
    let filteredNodes = this.applyFilters(nodes, config);

    // 过滤父子冲突
    filteredNodes = this.filterParentChildConflicts(filteredNodes);

    // 按优先级排序
    return this.prioritizeNodes(filteredNodes);
  }

  /**
   * 视口优先扫描方法
   * 🚀 性能优化：优先处理视口内可见内容，后台异步处理其余部分
   * @param config 扫描配置
   * @returns Promise<ViewportScanResult> 包含视口节点和后台扫描Promise
   */
  async scanViewportFirst(config: ScanConfig = {}): Promise<ViewportScanResult> {
    const startTime = performance.now();
    
    if (this.debugMode) {
      this.logger.info('Starting viewport-first scan');
    }

    // 计算视口边界
    const viewport = this.calculateViewportBounds(config.viewportMargin || 200);
    
    // 使用小分块快速扫描全部内容
    const allNodes = await this.scanAsync(config, 50);
    
    // 将节点按视口内外分区
    const { viewportNodes, backgroundNodes } = this.partitionByViewport(allNodes, viewport);
    
    // 创建后台扫描Promise (这里直接返回已分区的背景节点)
    const backgroundScanPromise = Promise.resolve(backgroundNodes);
    
    const duration = performance.now() - startTime;
    
    if (this.debugMode) {
      this.logger.info('Viewport-first scan completed', {
        viewportNodes: viewportNodes.length,
        backgroundNodes: backgroundNodes.length,
        duration: `${duration.toFixed(2)}ms`,
        viewportBounds: viewport
      });
    }

    return {
      viewportNodes,
      backgroundScanPromise
    };
  }

  /**
   * 增量扫描方法 - 处理MutationObserver事件
   * 🚀 性能优化：只扫描新增的DOM节点，避免重复扫描整个页面
   * @param addedNodes 新增的DOM节点
   * @returns Promise<ScannedNode[]> 新增的可翻译节点
   */
  async scanIncremental(addedNodes: Node[]): Promise<ScannedNode[]> {
    const startTime = performance.now();
    const results: ScannedNode[] = [];

    if (this.debugMode) {
      this.logger.info(`Starting incremental scan for ${addedNodes.length} added nodes`);
    }

    let position = 0;
    for (const node of addedNodes) {
      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as HTMLElement;
        
        // 分析新增元素
        const scannedNode = this.analyzeNode(element, position);
        if (scannedNode) {
          results.push(scannedNode);
          position++;
        }
        
        // 检查元素的子节点
        const childElements = element.querySelectorAll('*');
        for (const childElement of childElements) {
          const childScannedNode = this.analyzeNode(childElement as HTMLElement, position);
          if (childScannedNode) {
            results.push(childScannedNode);
            position++;
          }
        }
      }
      
      // 每处理10个节点让出一次执行权
      if (position % 10 === 0 && position > 0) {
        await this.yieldToMain();
      }
    }

    // 应用过滤规则
    const filteredResults = this.applyFiltersAndSort(results, {});
    
    const duration = performance.now() - startTime;
    
    if (this.debugMode) {
      this.logger.info('Incremental scan completed', {
        nodesAdded: addedNodes.length,
        translatableFound: results.length,
        finalCount: filteredResults.length,
        duration: `${duration.toFixed(2)}ms`
      });
    }

    return filteredResults;
  }

  /**
   * 计算视口边界
   * @private
   */
  private calculateViewportBounds(margin: number = 200): ViewportBounds {
    const scrollY = window.scrollY || window.pageYOffset;
    const scrollX = window.scrollX || window.pageXOffset;
    
    return {
      top: scrollY - margin,
      bottom: scrollY + window.innerHeight + margin,
      left: scrollX - margin,
      right: scrollX + window.innerWidth + margin
    };
  }

  /**
   * 根据视口边界分区节点
   * @private
   */
  private partitionByViewport(
    nodes: ScannedNode[], 
    viewport: ViewportBounds
  ): { viewportNodes: ScannedNode[]; backgroundNodes: ScannedNode[] } {
    const viewportNodes: ScannedNode[] = [];
    const backgroundNodes: ScannedNode[] = [];

    for (const node of nodes) {
      if (this.isElementInViewport(node.element, viewport)) {
        viewportNodes.push(node);
      } else {
        backgroundNodes.push(node);
      }
    }

    if (this.debugMode) {
      this.logger.info('Node partitioning completed', {
        total: nodes.length,
        viewport: viewportNodes.length,
        background: backgroundNodes.length,
        viewportRatio: `${((viewportNodes.length / nodes.length) * 100).toFixed(1)}%`
      });
    }

    return { viewportNodes, backgroundNodes };
  }

  /**
   * 检查元素是否在视口内
   * @private
   */
  private isElementInViewport(element: HTMLElement, viewport: ViewportBounds): boolean {
    try {
      const rect = element.getBoundingClientRect();
      const scrollY = window.scrollY || window.pageYOffset;
      const scrollX = window.scrollX || window.pageXOffset;
      
      // 将相对视口的坐标转换为绝对页面坐标
      const elementTop = rect.top + scrollY;
      const elementBottom = rect.bottom + scrollY;
      const elementLeft = rect.left + scrollX;
      const elementRight = rect.right + scrollX;
      
      // 检查是否与视口边界重叠
      const inViewport = !(
        elementBottom < viewport.top ||
        elementTop > viewport.bottom ||
        elementRight < viewport.left ||
        elementLeft > viewport.right
      );
      
      return inViewport;
    } catch (error) {
      // 如果getBoundingClientRect失败，默认认为不在视口内
      if (this.debugMode) {
        this.logger.warn('Failed to check viewport intersection', { error });
      }
      return false;
    }
  }

  /**
   * 异步调度机制 - 让出主线程执行权
   * 🚀 性能优化：使用现代浏览器的调度API，优雅降级到setTimeout
   * @private
   */
  private async yieldToMain(): Promise<void> {
    return new Promise((resolve) => {
      // 优先使用现代浏览器的调度API
      if ('scheduler' in window && 'postTask' in (window as any).scheduler) {
        (window as any).scheduler.postTask(resolve, {
          priority: 'user-blocking',
        });
      } else if ('MessageChannel' in window) {
        // 使用 MessageChannel 实现更精确的调度
        const channel = new MessageChannel();
        channel.port2.onmessage = () => resolve();
        channel.port1.postMessage(null);
      } else {
        // 降级到 setTimeout
        setTimeout(resolve, 0);
      }
    });
  }

  /**
   * 获取扫描器统计信息
   */
  getStats(): any {
    return {
      // 可以添加扫描器的内部统计信息
      scansPerformed: 0,
      lastScanDuration: 0,
      totalNodesScanned: 0
    };
  }

  /**
   * 检查元素是否包含其他可翻译的子元素（叶子节点验证）
   * 🔧 优化核心：实现"原子翻译块"原则，避免容器与内容同时被选中
   * @param element 待检查的元素
   * @param nodeElements 当前节点列表中的元素集合
   * @returns 如果包含块级可翻译子元素则返回true（应被排除）
   */
  private hasTranslatableChildren(element: HTMLElement, nodeElements: Set<HTMLElement>): boolean {
    // 🔧 核心优化：区分"原子内容块"与"容器元素"
    // 原子内容块（h1, p, li等）本身就是翻译的最小单元，不应被其子元素影响
    const atomicContentBlocks = new Set([
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', // 标题元素
      'p',                                  // 段落
      'li',                                 // 列表项
      'blockquote',                         // 引用块
      'figcaption',                         // 图片说明
      'td', 'th',                          // 表格单元格
      'dt', 'dd',                          // 定义列表项
      'caption',                           // 表格标题
      // 🔧 修复：内联元素作为原子内容块，但在父子冲突中优先选择叶子节点
      'span', 'strong', 'em', 'b', 'i', 'u', 'mark', // 内联文本元素
      'small', 'sub', 'sup', 'code'        // 其他内联元素
      // 注意：移除 'a' 标签，让链接元素可以被作为容器的可翻译子元素处理
    ]);

    const currentTag = element.tagName.toLowerCase();

    // 🎯 关键修复：如果当前元素本身是原子内容块，立即返回false
    // 这样可以确保h1、p等元素不会因为包含任何子元素而被排除
    if (atomicContentBlocks.has(currentTag)) {
      if (this.debugMode) {
        this.logger.info(`[Scanner] 保留原子内容块：${element.tagName}.${element.className || 'no-class'} (原子块不检查子元素)`);
      }
      return false;
    }

    // 只有非原子块元素（如div、section、article等容器）才检查是否包含可翻译子元素
    const translatableChildSelectors = [
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', // 标题
      'p',                                  // 段落
      'a',                                  // 🔧 关键修复：将链接元素作为可翻译子元素
      'li',                                 // 列表项
      'blockquote',                         // 引用
      'figcaption',                         // 图片说明
      'td', 'th',                          // 表格单元格
      'dt', 'dd',                          // 定义列表项
      'caption',                           // 表格标题
      'div[data-translatable]'             // 显式标记的div
    ];

    // 检查容器是否包含原子内容块作为直接子元素
    for (const selector of translatableChildSelectors) {
      try {
        // 使用 :scope > 确保只匹配直接子元素，避免深度遍历
        const directChildren = element.querySelectorAll(`:scope > ${selector}`);
        if (directChildren.length > 0) {
          // 验证这些子元素是否真的可翻译（有足够文本且在节点列表中）
          const hasValidChild = Array.from(directChildren).some(child => {
            const childElement = child as HTMLElement;
            const childText = childElement.textContent?.trim() || '';
            return childText.length > 5 && nodeElements.has(childElement); // 降低文本长度要求到5
          });

          if (hasValidChild) {
            if (this.debugMode) {
              this.logger.info(`[Scanner] 排除容器元素：${element.tagName}.${element.className || 'no-class'}，包含可翻译子元素 (${selector})`);
            }
            return true;
          }
        }
      } catch (error) {
        // 忽略选择器错误，继续检查下一个
        continue;
      }
    }

    return false;
  }

  /**
   * 重置扫描器状态
   */
  reset(): void {
    // 重置内部状态（如果有的话）
    if (this.debugMode) {
      this.logger.info('DomScanner reset');
    }
  }

  /**
   * 销毁扫描器
   */
  destroy(): void {
    // 清理资源
    if (this.debugMode) {
      this.logger.info('DomScanner destroyed');
    }
  }
}