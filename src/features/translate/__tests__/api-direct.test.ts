/**
 * 直接API调用测试
 * 验证翻译引擎API调用逻辑和响应处理
 * 绕过复杂的引擎实现，直接测试API交互
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { GoogleTranslateEngine } from '../engines/google';
import { MicrosoftTranslateEngine } from '../engines/microsoft';
import { TranslateConfigManager } from '../config';
import { GoogleEngineConfig, MicrosoftEngineConfig } from '../types';

// Mock fetch entirely for this test
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock browser runtime
const mockBrowserRuntime = {
  sendMessage: vi.fn(),
  lastError: null
};

Object.defineProperty(global, 'browser', {
  value: { runtime: mockBrowserRuntime },
  writable: true,
});

describe('直接API调用测试', () => {
  let configManager: TranslateConfigManager;

  beforeEach(() => {
    vi.clearAllMocks();
    configManager = new TranslateConfigManager();
  });

  describe('Google引擎API调用处理', () => {
    let googleEngine: GoogleTranslateEngine;

    beforeEach(() => {
      const config = configManager.getEngineConfig('google') as GoogleEngineConfig;
      googleEngine = new GoogleTranslateEngine(config);
    });

    it('应该正确构建Google API 1的protobuf请求体', () => {
      const texts = ['Hello', 'World'];
      const options = { from: 'en', to: 'zh' };
      
      // 使用类型断言来访问私有方法
      const buildBody = (googleEngine as any)['buildGoogleApi1Body'];
      const body = buildBody(texts, options);

      expect(typeof body).toBe('string');
      const parsed = JSON.parse(body);
      
      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(2);
      expect(parsed[0]).toEqual([texts, 'en', 'zh']);
      expect(parsed[1]).toBe('te_lib');
    });

    it('应该正确构建Google API 2的form请求体', () => {
      const texts = ['Hello', 'World'];
      const options = { from: 'en', to: 'zh' };
      
      // 使用类型断言来访问私有方法
      const buildBody = (googleEngine as any)['buildGoogleApi2Body'];
      const body = buildBody(texts, options);

      expect(typeof body).toBe('string');
      
      // 验证form-urlencoded格式
      const params = new URLSearchParams(body);
      expect(params.getAll('q')).toEqual(texts);
      expect(params.get('sl')).toBe('en');
      expect(params.get('tl')).toBe('zh');
    });

    it('应该正确解析Google API 1的protobuf响应', () => {
      const mockResponse = [
        [
          [
            ['你好', 'Hello', null, null, 1],
            ['世界', 'World', null, null, 1]
          ]
        ],
        'te_lib'
      ];
      const originalTexts = ['Hello', 'World'];

      // 使用类型断言来访问私有方法
      const parseResponse = (googleEngine as any)['parseGoogleApi1Response'];
      const result = parseResponse(mockResponse, originalTexts);

      expect(Array.isArray(result)).toBe(true);
      expect(result).toEqual(['你好', '世界']);
    });

    it('应该正确解析Google API 2的文本响应', () => {
      const mockResponseText = '[[["你好","Hello",null,null,1],["世界","World",null,null,1]]]';
      const originalTexts = ['Hello', 'World'];

      // 使用类型断言来访问私有方法
      const parseResponse = (googleEngine as any)['parseGoogleApi2Response'];
      const result = parseResponse(mockResponseText, originalTexts);

      expect(Array.isArray(result)).toBe(true);
      expect(result).toEqual(['你好', '世界']);
    });

    it('应该处理无效的响应格式', () => {
      const invalidResponse = null;
      const originalTexts = ['Hello', 'World'];

      // 使用类型断言来访问私有方法
      const parseResponse = (googleEngine as any)['parseGoogleApi1Response'];
      const result = parseResponse(invalidResponse, originalTexts);

      // 应该返回原文本作为降级
      expect(result).toEqual(originalTexts);
    });
  });

  describe('Microsoft引擎API调用处理', () => {
    let microsoftEngine: MicrosoftTranslateEngine;

    beforeEach(() => {
      const config = configManager.getEngineConfig('microsoft') as MicrosoftEngineConfig;
      // 启用Microsoft引擎用于测试
      config.enabled = true;
      microsoftEngine = new MicrosoftTranslateEngine(config);
    });

    it('应该正确构建Microsoft API的JSON请求体', () => {
      const texts = ['Hello', 'World'];
      
      // 使用类型断言来访问私有方法
      const buildBody = (microsoftEngine as any)['buildMicrosoftBody'];
      const body = buildBody(texts);

      expect(Array.isArray(body)).toBe(true);
      expect(body).toHaveLength(2);
      expect(body[0]).toEqual({ Text: 'Hello' });
      expect(body[1]).toEqual({ Text: 'World' });
    });

    it('应该正确构建Microsoft API的URL', () => {
      const options = { from: 'en', to: 'zh' };
      const baseUrl = 'https://api.cognitive.microsofttranslator.com/translate';
      
      // 使用类型断言来访问私有方法
      const buildUrl = (microsoftEngine as any)['buildMicrosoftUrl'];
      const url = buildUrl(options, baseUrl);

      expect(url).toContain('api-version=3.0');
      expect(url).toContain('from=en');
      expect(url).toContain('to=zh');
    });

    it('应该正确构建Microsoft API的请求头', () => {
      // 先设置一个mock token
      (microsoftEngine as any).authToken = 'test-token-123';
      
      // 使用类型断言来访问私有方法
      const buildHeaders = (microsoftEngine as any)['buildMicrosoftHeaders'];
      const headers = buildHeaders();

      expect(headers['Content-Type']).toBe('application/json');
      expect(headers['Authorization']).toBe('Bearer test-token-123');
      expect(headers['Accept']).toBe('*/*');
      expect(headers['User-Agent']).toContain('Mozilla');
    });

    it('应该正确解析Microsoft API响应', () => {
      const mockResponse = [
        {
          translations: [{ text: '你好', to: 'zh-Hans' }]
        },
        {
          translations: [{ text: '世界', to: 'zh-Hans' }]
        }
      ];
      const originalTexts = ['Hello', 'World'];

      // 使用类型断言来访问私有方法
      const parseResponse = (microsoftEngine as any)['parseMicrosoftResponse'];
      const result = parseResponse(mockResponse, originalTexts);

      expect(Array.isArray(result)).toBe(true);
      expect(result).toEqual(['你好', '世界']);
    });

    it('应该处理Microsoft API的错误响应', () => {
      const invalidResponse = [
        { error: 'Translation failed' }
      ];
      const originalTexts = ['Hello'];

      // 使用类型断言来访问私有方法
      const parseResponse = (microsoftEngine as any)['parseMicrosoftResponse'];
      const result = parseResponse(invalidResponse, originalTexts);

      // 应该返回原文本作为降级
      expect(result).toEqual(['Hello']);
    });

    it('应该正确检测认证错误', () => {
      const authError = new Error('Unauthorized access');
      const networkError = new Error('Network connection failed');

      // 使用类型断言来访问私有方法
      const isAuthError = (microsoftEngine as any)['isAuthError'];
      
      expect(isAuthError(authError)).toBe(true);
      expect(isAuthError(networkError)).toBe(false);
    });
  });

  describe('API端点配置验证', () => {
    it('Google引擎应该有正确的API端点配置', () => {
      const config = configManager.getEngineConfig('google') as GoogleEngineConfig;
      
      expect(config.apis).toHaveLength(2);
      expect(config.apis[0].name).toBe('google-translate-api-1');
      expect(config.apis[0].url).toContain('translateHtml');
      expect(config.apis[1].name).toBe('google-translate-api-2');
      expect(config.apis[1].url).toContain('translate_a/t');
    });

    it('Microsoft引擎应该有正确的API端点配置', () => {
      const config = configManager.getEngineConfig('microsoft') as MicrosoftEngineConfig;
      
      expect(config.apis).toHaveLength(1);
      expect(config.apis[0].name).toBe('azure-translator');
      expect(config.apis[0].url).toContain('cognitive.microsofttranslator.com');
    });

    it('引擎配置应该包含必要的属性', () => {
      const googleConfig = configManager.getEngineConfig('google') as GoogleEngineConfig;
      const msConfig = configManager.getEngineConfig('microsoft') as MicrosoftEngineConfig;

      // 验证必要属性存在
      ['name', 'enabled', 'priority', 'maxChunkSize', 'maxBatchSize', 'timeout', 'retryCount', 'apis'].forEach(prop => {
        expect(googleConfig).toHaveProperty(prop);
        expect(msConfig).toHaveProperty(prop);
      });

      // 验证API配置
      googleConfig.apis.forEach(api => {
        expect(api).toHaveProperty('name');
        expect(api).toHaveProperty('url');
        expect(api).toHaveProperty('priority');
        expect(api).toHaveProperty('enabled');
        expect(api).toHaveProperty('timeout');
      });
    });
  });
});