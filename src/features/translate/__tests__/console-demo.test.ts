/**
 * 控制台演示测试
 * 专门用于在控制台中展示翻译功能的测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { translateService } from '../index';

// Mock browser APIs
const mockBrowserStorage = {
  local: {
    get: vi.fn(),
    set: vi.fn(),
  },
};

const mockBrowserRuntime = {
  sendMessage: vi.fn(),
};

Object.defineProperty(global, 'browser', {
  value: {
    storage: mockBrowserStorage,
    runtime: mockBrowserRuntime,
  },
  writable: true,
});

describe('翻译功能控制台演示', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock successful storage
    mockBrowserStorage.local.get.mockResolvedValue({});
    mockBrowserStorage.local.set.mockResolvedValue(undefined);
  });

  it('应该在控制台显示翻译结果', async () => {
    console.log('\n🚀 翻译功能演示开始');
    console.log('=' .repeat(50));
    
    // Mock successful translation
    mockBrowserRuntime.sendMessage.mockResolvedValue({
      success: true,
      data: [[[['你好', 'Hello', null, null, 1]]]],
      status: 200,
      statusText: 'OK'
    });

    console.log('📝 测试文本: "Hello"');
    console.log('🎯 目标语言: 中文 (zh)');
    console.log('🔧 使用引擎: Google');
    
    const startTime = Date.now();
    const result = await translateService.translateText('Hello', {
      from: 'en',
      to: 'zh',
      engine: 'google'
    });
    const duration = Date.now() - startTime;

    console.log('\n✅ 翻译成功！');
    console.log(`🌟 翻译结果: "${result}"`);
    console.log(`🔧 引擎: google`);
    console.log(`⏱️ 耗时: ${duration}ms`);
    
    console.log('\n📊 服务统计:');
    const stats = translateService.getServiceStats();
    console.log(`  总请求: ${stats.totalRequests}`);
    console.log(`  成功请求: ${stats.successfulRequests}`);
    console.log(`  失败请求: ${stats.failedRequests}`);
    console.log(`  成功率: ${Math.round(stats.successfulRequests / stats.totalRequests * 100)}%`);
    
    console.log('\n🎯 引擎状态:');
    const engines = translateService.getAvailableEngines();
    console.log(`  可用引擎: ${engines.join(', ')}`);
    
    const recommended = translateService.getRecommendedEngine();
    console.log(`  推荐引擎: ${recommended}`);
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎉 翻译功能演示完成');
    
    // 验证翻译结果
    expect(result).toBe('你好');
  });

  it('应该演示批量翻译功能', async () => {
    console.log('\n🚀 批量翻译演示开始');
    console.log('=' .repeat(50));
    
    const testTexts = [
      'Hello',
      'Good morning',
      'How are you?',
      'Nice to meet you',
      'Thank you very much'
    ];
    
    // Mock successful batch translation - 修复以匹配新的API
    mockBrowserRuntime.sendMessage.mockResolvedValue({
      success: true,
      data: [
        ['你好', '早上好', '你好吗？', '很高兴见到你', '非常感谢']
      ],
      status: 200,
      statusText: 'OK'
    });

    console.log('📝 测试文本列表:');
    testTexts.forEach((text, index) => {
      console.log(`  ${index + 1}. "${text}"`);
    });
    
    console.log('\n🎯 目标语言: 中文 (zh)');
    console.log('🔧 使用引擎: Google');
    
    const startTime = Date.now();
    const result = await translateService.translateTexts(testTexts, {
      from: 'en',
      to: 'zh',
      engine: 'google'
    });
    const duration = Date.now() - startTime;

    console.log('\n✅ 批量翻译成功！');
    console.log('\n📋 翻译对照表:');
    testTexts.forEach((text, index) => {
      const translation = result[index];
      console.log(`  ${index + 1}. "${text}" → "${translation}"`);
    });
    
    console.log('\n📊 翻译统计:');
    console.log(`  📝 原文数量: ${testTexts.length}`);
    console.log(`  🌟 译文数量: ${result.length}`);
    console.log(`  🔧 使用引擎: google`);
    console.log(`  ⏱️ 总耗时: ${duration}ms`);
    console.log(`  🚀 平均每条: ${Math.round(duration / testTexts.length)}ms`);
    console.log(`  🎯 吞吐量: ${Math.round(testTexts.length / duration * 1000)} texts/sec`);
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎉 批量翻译演示完成');
    
    // 验证翻译结果 - 修复以匹配新的API
    expect(result).toHaveLength(5);
    expect(result[0]).toBe('你好');
    expect(result[1]).toBe('早上好');
  });

  it('应该演示错误处理', async () => {
    console.log('\n🚀 错误处理演示开始');
    console.log('=' .repeat(50));
    
    // Mock API error
    mockBrowserRuntime.sendMessage.mockRejectedValue(new Error('API服务暂时不可用'));

    console.log('📝 测试文本: "Hello"');
    console.log('🎯 目标语言: 中文 (zh)');
    console.log('🔧 使用引擎: Google');
    console.log('⚠️ 模拟API错误...');
    
    try {
      await translateService.translateText('Hello', {
        from: 'en',
        to: 'zh',
        engine: 'google'
      });
      // 如果没有抛出错误，说明测试未按预期进行
      console.log('\n⚠️ 预期错误未发生');
    } catch (error: any) {
      console.log('\n❌ 翻译失败（预期行为）');
      console.log(`🔍 错误类型: ${error?.constructor?.name || 'unknown'}`);
      console.log(`📋 错误信息: ${error?.message || 'unknown error'}`);
      console.log(`🔧 相关引擎: ${error?.engine || 'unknown'}`);
      console.log(`📊 错误类型: ${error?.type || 'unknown'}`);
      
      // 显示服务统计中的错误计数
      const stats = translateService.getServiceStats();
      console.log('\n📊 错误统计:');
      console.log(`  总请求: ${stats.totalRequests}`);
      console.log(`  成功请求: ${stats.successfulRequests}`);
      console.log(`  失败请求: ${stats.failedRequests}`);
      console.log(`  错误类型分布: ${JSON.stringify(stats.errorsByType)}`);
      
      // 验证确实捕获了错误
      expect(error?.message).toContain('API服务暂时不可用');
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎉 错误处理演示完成');
  });

  it('应该展示完整的翻译系统功能', async () => {
    console.log('\n🎬 完整翻译系统功能展示');
    console.log('=' .repeat(60));
    
    // 1. 系统状态
    console.log('\n📋 1. 系统状态检查');
    const engines = translateService.getAvailableEngines();
    console.log(`✅ 可用引擎: ${engines.join(', ')}`);
    
    const config = translateService.getConfigManager().getConfig();
    console.log(`🔧 默认配置: ${config.defaultSourceLanguage} → ${config.defaultTargetLanguage}`);
    
    // 2. 单文本翻译
    console.log('\n📋 2. 单文本翻译测试');
    mockBrowserRuntime.sendMessage.mockResolvedValue({
      success: true,
      data: ['你好，世界！'],
      status: 200,
      statusText: 'OK'
    });
    
    const singleResult = await translateService.translateText('Hello, World!', {
      from: 'en',
      to: 'zh'
    });
    
    console.log(`✅ 单文本翻译: "${singleResult}"`);
    
    // 3. 批量翻译
    console.log('\n📋 3. 批量翻译测试');
    mockBrowserRuntime.sendMessage.mockResolvedValue({
      success: true,
      data: ['早上好', '下午好', '晚上好'],
      status: 200,
      statusText: 'OK'
    });
    
    const batchResult = await translateService.translateTexts([
      'Good morning',
      'Good afternoon', 
      'Good evening'
    ], { from: 'en', to: 'zh' });
    
    console.log('✅ 批量翻译结果:');
    batchResult.forEach((translation, index) => {
      const original = ['Good morning', 'Good afternoon', 'Good evening'][index];
      console.log(`  "${original}" → "${translation}"`);
    });
    
    // 4. 引擎状态
    console.log('\n📋 4. 引擎状态检查');
    const recommended = translateService.getRecommendedEngine();
    console.log(`🎯 推荐引擎: ${recommended}`);
    
    // 5. 服务统计
    console.log('\n📋 5. 服务统计报告');
    const stats = translateService.getServiceStats();
    console.log(`📊 总请求: ${stats.totalRequests}`);
    console.log(`✅ 成功请求: ${stats.successfulRequests}`);
    console.log(`❌ 失败请求: ${stats.failedRequests}`);
    console.log(`⏱️ 平均延迟: ${Math.round(stats.averageLatency)}ms`);
    console.log(`🔧 引擎使用统计: ${JSON.stringify(stats.engineUsage)}`);
    
    const successRate = Math.round(stats.successfulRequests / stats.totalRequests * 100);
    console.log(`🎯 总体成功率: ${successRate}%`);
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 完整功能展示完成！');
    console.log(`🌟 系统运行正常，成功率: ${successRate}%`);
    
    // 验证整体功能
    expect(singleResult).toBe('你好，世界！');
    expect(batchResult).toHaveLength(3);
    expect(batchResult[0]).toBe('早上好');
    expect(successRate).toBeGreaterThan(80);
    
    console.log('\n💡 提示: 所有测试已通过，翻译系统运行正常！');
  });
});