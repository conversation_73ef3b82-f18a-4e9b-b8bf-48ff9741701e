/**
 * 全面的翻译API测试
 * 测试所有三个翻译API的一致性和可靠性
 * 
 * 涵盖的API：
 * 1. Google Translate API 1 (translateHtml) - protobuf格式
 * 2. Google Translate API 2 (translate_a/t) - form-urlencoded格式  
 * 3. Microsoft Translate API - JSON格式
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { GoogleTranslateEngine } from '../engines/google';
import { MicrosoftTranslateEngine } from '../engines/microsoft';
import { TranslateService } from '../translate.service';
import { TranslateConfigManager } from '../config';
import { GoogleEngineConfig, MicrosoftEngineConfig } from '../types';

// Mock browser runtime to return null so engines use direct fetch
const mockBrowserRuntime = null;

Object.defineProperty(global, 'browser', {
  value: null,
  writable: true,
});

// Mock fetch - this will be used by engines
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('全翻译API一致性测试', () => {
  let googleEngine: GoogleTranslateEngine;
  let microsoftEngine: MicrosoftTranslateEngine;
  let translateService: TranslateService;
  let configManager: TranslateConfigManager;

  const testTexts = [
    'Hello, World!',
    'How are you today?',
    'This is a test sentence.',
    'Good morning, everyone!'
  ];

  const expectedTranslations = [
    '你好，世界！',
    '你今天好吗？',
    '这是一个测试句子。',
    '大家早上好！'
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // 初始化配置管理器
    configManager = new TranslateConfigManager();
    
    // 初始化Google引擎
    const googleConfig = configManager.getEngineConfig('google') as GoogleEngineConfig;
    googleEngine = new GoogleTranslateEngine(googleConfig);
    
    // 初始化Microsoft引擎
    const microsoftConfig = configManager.getEngineConfig('microsoft') as MicrosoftEngineConfig;
    microsoftConfig.enabled = true; // 启用Microsoft引擎用于测试
    microsoftEngine = new MicrosoftTranslateEngine(microsoftConfig);
    
    // 初始化翻译服务
    translateService = new TranslateService(configManager);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Google Translate API 1 (translateHtml)', () => {
    beforeEach(() => {
      // Mock Google API 1 响应 (protobuf格式)
      mockFetch.mockImplementation((url, options) => {
        if (url.includes('translateHtml')) {
          return Promise.resolve({
            ok: true,
            status: 200,
            json: async () => [
              [
                [
                  ['你好，世界！', 'Hello, World!', null, null, 1],
                  ['你今天好吗？', 'How are you today?', null, null, 1],
                  ['这是一个测试句子。', 'This is a test sentence.', null, null, 1],
                  ['大家早上好！', 'Good morning, everyone!', null, null, 1]
                ]
              ],
              'te_lib'
            ]
          });
        }
        return Promise.reject(new Error('Unhandled URL in mock'));
      });
    });

    it('应该正确翻译单个文本', async () => {
      const result = await googleEngine.translateBatch(['Hello, World!'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('google');
      expect(result.translations).toHaveLength(1);
      expect(result.translations[0]).toBe('你好，世界！');
      expect(result.duration).toBeGreaterThan(0);
    }, 15000);

    it('应该正确翻译批量文本', async () => {
      const result = await googleEngine.translateBatch(testTexts, {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('google');
      expect(result.translations).toHaveLength(testTexts.length);
      expect(result.translations).toEqual(expectedTranslations);
    });

    it('应该正确构建protobuf格式的请求体', () => {
      const body = (googleEngine as any)['buildGoogleApi1Body'](testTexts, {
        from: 'en',
        to: 'zh'
      });

      expect(typeof body).toBe('string');
      const parsed = JSON.parse(body);
      
      // 验证protobuf格式结构
      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(2);
      expect(Array.isArray(parsed[0])).toBe(true);
      expect(parsed[0][0]).toEqual(testTexts);
      expect(parsed[0][1]).toBe('en');
      expect(parsed[0][2]).toBe('zh');
      expect(parsed[1]).toBe('te_lib');
    });

    it('应该包含正确的请求头', async () => {
      await googleEngine.translateBatch(['test'], { from: 'en', to: 'zh' });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('translateHtml'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json+protobuf',
            'x-goog-api-key': expect.any(String),
            'Origin': expect.stringContaining('chrome-extension')
          })
        })
      );
    });
  });

  describe('Google Translate API 2 (translate_a/t)', () => {
    beforeEach(() => {
      // Mock API 2 responses with fallback behavior
      mockFetch.mockImplementation((url, options) => {
        if (url.includes('translateHtml')) {
          return Promise.reject(new Error('API 1 failed'));
        }
        if (url.includes('translate_a/t')) {
          return Promise.resolve({
            ok: true,
            status: 200,
            text: async () => `[[["你好，世界！","Hello, World!",null,null,1]]]`
          });
        }
        return Promise.reject(new Error('Unhandled URL in mock'));
      });
    });

    it('应该在API 1失败时降级到API 2', async () => {
      const result = await googleEngine.translateBatch(['Hello, World!'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations[0]).toBe('你好，世界！');
      expect(mockFetch).toHaveBeenCalledTimes(2); // 第一次失败，第二次成功
    });

    it('应该正确构建form-urlencoded格式的请求体', () => {
      const body = (googleEngine as any)['buildGoogleApi2Body'](testTexts, {
        from: 'en',
        to: 'zh'
      });

      expect(typeof body).toBe('string');
      
      // 验证form-urlencoded格式
      const params = new URLSearchParams(body);
      testTexts.forEach((text, index) => {
        expect(params.getAll('q')).toContain(text);
      });
    });

    it('应该包含正确的URL参数', () => {
      const url = (googleEngine as any)['buildGoogleApi2Url']({
        from: 'en',
        to: 'zh'
      }, 'https://translate.googleapis.com/translate_a/t');

      expect(url).toContain('client=gtx');
      expect(url).toContain('dt=t');
      expect(url).toContain('sl=en');
      expect(url).toContain('tl=zh');
      expect(url).toContain('format=html');
    });
  });

  describe('Microsoft Translate API', () => {
    beforeEach(() => {
      // Mock Microsoft API responses
      mockFetch.mockImplementation((url, options) => {
        if (url.includes('edge.microsoft.com/translate/auth')) {
          return Promise.resolve({
            ok: true,
            text: async () => 'mock-auth-token-12345'
          });
        }
        if (url.includes('cognitive.microsofttranslator.com/translate')) {
          return Promise.resolve({
            ok: true,
            status: 200,
            json: async () => [
              { translations: [{ text: '你好，世界！' }] },
              { translations: [{ text: '你今天好吗？' }] },
              { translations: [{ text: '这是一个测试句子。' }] },
              { translations: [{ text: '大家早上好！' }] }
            ]
          });
        }
        return Promise.reject(new Error('Unhandled URL in mock'));
      });
    });

    it('应该正确获取认证token', async () => {
      const result = await microsoftEngine.translateBatch(['Hello, World!'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      
      // 第一次调用应该是获取token
      expect(mockFetch).toHaveBeenCalledWith(
        'https://edge.microsoft.com/translate/auth',
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('应该正确翻译文本', async () => {
      const result = await microsoftEngine.translateBatch(testTexts, {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('microsoft');
      expect(result.translations).toHaveLength(testTexts.length);
      expect(result.translations).toEqual(expectedTranslations);
    });

    it('应该正确构建JSON格式的请求体', () => {
      const body = (microsoftEngine as any)['buildMicrosoftBody'](testTexts);

      expect(Array.isArray(body)).toBe(true);
      expect(body).toHaveLength(testTexts.length);
      
      body.forEach((item: any, index: number) => {
        expect(item).toEqual({ Text: testTexts[index] });
      });
    });

    it('应该包含Bearer token在请求头中', async () => {
      await microsoftEngine.translateBatch(['test'], { from: 'en', to: 'zh' });

      // 检查翻译请求（第二次fetch调用）
      const translationCall = mockFetch.mock.calls[1];
      expect(translationCall).toBeDefined();
      expect(translationCall[1].headers).toEqual(
        expect.objectContaining({
          'Authorization': 'Bearer mock-auth-token',
          'Content-Type': 'application/json'
        })
      );
    });

    it('应该在认证失败时重新获取token', async () => {
      // 第一次翻译请求失败（401错误）
      mockFetch
        .mockResolvedValueOnce({ ok: true, text: async () => 'token1' })
        .mockRejectedValueOnce(new Error('Unauthorized'))
        .mockResolvedValueOnce({ ok: true, text: async () => 'token2' })
        .mockResolvedValue({
          ok: true,
          json: async () => [{ translations: [{ text: '你好' }] }]
        });

      const result = await microsoftEngine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(4); // token1, 失败, token2, 成功
    });
  });

  describe('API包装一致性测试', () => {
    const testOptions = { from: 'en', to: 'zh' };

    beforeEach(() => {
      // Mock所有API都成功响应
      mockFetch
        // Microsoft auth token
        .mockResolvedValueOnce({
          ok: true,
          text: async () => 'mock-token'
        })
        // Google API 1 response
        .mockResolvedValueOnce({
          ok: true,
          json: async () => [[[['测试', 'test', null, null, 1]]], 'te_lib']
        })
        // Microsoft API response  
        .mockResolvedValue({
          ok: true,
          json: async () => [{ translations: [{ text: '测试' }] }]
        });
    });

    it('所有引擎应该返回相同格式的响应', async () => {
      const googleResult = await googleEngine.translateBatch(['test'], testOptions);
      const microsoftResult = await microsoftEngine.translateBatch(['test'], testOptions);

      // 验证响应格式一致性
      const commonFields = ['translations', 'engine', 'success', 'duration'];
      
      commonFields.forEach(field => {
        expect(googleResult).toHaveProperty(field);
        expect(microsoftResult).toHaveProperty(field);
      });

      // 验证类型一致性
      expect(Array.isArray(googleResult.translations)).toBe(true);
      expect(Array.isArray(microsoftResult.translations)).toBe(true);
      expect(typeof googleResult.engine).toBe('string');
      expect(typeof microsoftResult.engine).toBe('string');
      expect(typeof googleResult.success).toBe('boolean');
      expect(typeof microsoftResult.success).toBe('boolean');
      expect(typeof googleResult.duration).toBe('number');
      expect(typeof microsoftResult.duration).toBe('number');
    });

    it('所有引擎应该正确处理相同的输入', async () => {
      const testInput = ['Hello', 'World'];
      
      const googleResult = await googleEngine.translateBatch(testInput, testOptions);
      const microsoftResult = await microsoftEngine.translateBatch(testInput, testOptions);

      expect(googleResult.translations).toHaveLength(testInput.length);
      expect(microsoftResult.translations).toHaveLength(testInput.length);
      expect(googleResult.success).toBe(true);
      expect(microsoftResult.success).toBe(true);
    });

    it('翻译服务应该能够在引擎间切换', async () => {
      // 注册所有引擎
      translateService.registerEngine(googleEngine);
      translateService.registerEngine(microsoftEngine);

      // 设置引擎优先级
      await translateService.setEnginePriority(['google', 'microsoft']);

      const result = await translateService.translateText('test', testOptions);
      expect(typeof result).toBe('string');
      expect(result).toBe('测试');
    });
  });

  describe('错误处理一致性', () => {
    it('所有引擎应该正确处理网络错误', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      await expect(googleEngine.translateBatch(['test'], { from: 'en', to: 'zh' }))
        .rejects.toThrow();
      
      await expect(microsoftEngine.translateBatch(['test'], { from: 'en', to: 'zh' }))
        .rejects.toThrow();
    });

    it('所有引擎应该正确处理API错误响应', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      });

      await expect(googleEngine.translateBatch(['test'], { from: 'en', to: 'zh' }))
        .rejects.toThrow();
      
      await expect(microsoftEngine.translateBatch(['test'], { from: 'en', to: 'zh' }))
        .rejects.toThrow();
    });

    it('所有引擎应该正确处理超时', async () => {
      // Mock超时
      mockFetch.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );

      await expect(googleEngine.translateBatch(['test'], { from: 'en', to: 'zh' }))
        .rejects.toThrow();
      
      await expect(microsoftEngine.translateBatch(['test'], { from: 'en', to: 'zh' }))
        .rejects.toThrow();
    });
  });

  describe('性能一致性', () => {
    beforeEach(() => {
      // Mock快速响应
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => [[[['测试', 'test', null, null, 1]]], 'te_lib'],
        text: async () => 'mock-token'
      });
    });

    it('所有引擎应该在合理时间内完成翻译', async () => {
      const timeout = 5000; // 5秒超时

      const googlePromise = googleEngine.translateBatch(['test'], { from: 'en', to: 'zh' });
      const microsoftPromise = microsoftEngine.translateBatch(['test'], { from: 'en', to: 'zh' });

      await expect(Promise.race([
        googlePromise,
        new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), timeout))
      ])).resolves.toBeDefined();

      await expect(Promise.race([
        microsoftPromise,
        new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), timeout))
      ])).resolves.toBeDefined();
    });

    it('所有引擎的响应时间应该被正确记录', async () => {
      const googleResult = await googleEngine.translateBatch(['test'], { from: 'en', to: 'zh' });
      const microsoftResult = await microsoftEngine.translateBatch(['test'], { from: 'en', to: 'zh' });

      expect(googleResult.duration).toBeGreaterThan(0);
      expect(microsoftResult.duration).toBeGreaterThan(0);
      expect(typeof googleResult.duration).toBe('number');
      expect(typeof microsoftResult.duration).toBe('number');
    });
  });
});