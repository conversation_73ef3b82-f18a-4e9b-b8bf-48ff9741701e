/**
 * 关键问题修复验证测试 - 简化版
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createTranslationCacheManager } from '../cache-manager';

describe('🔧 关键问题修复验证', () => {
  let consoleSpy: vi.SpyInstance;
  let consoleWarnSpy: vi.SpyInstance;
  
  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });
  
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('🚨 关键问题1: 自旋锁死锁风险修复', () => {
    it('✅ 应该在自旋锁超时后直接返回避免数据不一致', async () => {
      const cacheManager = createTranslationCacheManager({
        spinLockTimeoutMs: 50
      });
      
      try {
        // 模拟statsLock被长时间占用的情况
        (cacheManager as any).statsLock = true;
        
        // 执行统计更新，应该超时返回
        await (cacheManager as any).updateStatsAtomic(1, 0);
        
        // 验证超时行为
        expect(consoleWarnSpy).toHaveBeenCalledWith(
          expect.stringContaining('统计更新自旋锁超时，跳过本次更新')
        );
        
        // 验证统计数据没有被错误更新
        const stats = cacheManager.getStats();
        expect(stats.hits).toBe(0);
      } finally {
        await cacheManager.destroy();
      }
    });
    
    it('⚡ 应该在正常情况下成功更新统计数据', async () => {
      const cacheManager = createTranslationCacheManager();
      
      try {
        await (cacheManager as any).updateStatsAtomic(5, 3);
        
        const stats = cacheManager.getStats();
        expect(stats.hits).toBe(5);
        expect(stats.misses).toBe(3);
      } finally {
        await cacheManager.destroy();
      }
    });
  });

  describe('🚨 关键问题2: 异步操作未正确清理修复', () => {
    it('🔄 应该正确清理定时器', async () => {
      const cacheManager = createTranslationCacheManager({
        cleanupIntervalHours: 0.01
      });
      
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      await cacheManager.destroy();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
      
      clearIntervalSpy.mockRestore();
    });
    
    it('🧹 应该清理内存缓存', async () => {
      const cacheManager = createTranslationCacheManager();
      
      await cacheManager.set('test1', 'en', 'zh', 'google', 'result1');
      await cacheManager.set('test2', 'en', 'zh', 'google', 'result2');
      
      expect(cacheManager.getStats().size).toBe(2);
      
      await cacheManager.destroy();
      
      expect(cacheManager.getStats().size).toBe(0);
    });
    
    it('📊 应该在销毁时输出确认日志', async () => {
      const cacheManager = createTranslationCacheManager();
      
      await cacheManager.destroy();
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('缓存管理器已安全销毁')
      );
    });
  });

  describe('🔍 资源清理和生命周期管理', () => {
    it('🔄 应该支持多次销毁调用而不出错', async () => {
      const cacheManager = createTranslationCacheManager();
      
      await cacheManager.destroy();
      await expect(cacheManager.destroy()).resolves.not.toThrow();
    });
  });

  describe('💾 存储配额耗尽边界情况', () => {
    it('🔄 应该在存储错误后继续正常工作', async () => {
      const cacheManager = createTranslationCacheManager();
      
      try {
        // 即使存储失败，内存缓存应该正常工作
        await cacheManager.set('test', 'en', 'zh', 'google', 'result');
        const result = await cacheManager.get('test', 'en', 'zh', 'google');
        
        expect(result).toBe('result');
        expect(cacheManager.getStats().size).toBe(1);
      } finally {
        await cacheManager.destroy();
      }
    });
  });

  describe('🕰️ 长期运行内存泄漏预防', () => {
    it('📈 应该监控内存使用增长', async () => {
      const cacheManager = createTranslationCacheManager();
      
      try {
        for (let i = 0; i < 10; i++) {
          await cacheManager.set(
            `test${i}`.repeat(10),
            'en', 'zh', 'google', 
            `result${i}`.repeat(10)
          );
        }
        
        const healthMetrics = cacheManager.getHealthMetrics();
        
        expect(healthMetrics.memoryUsage).toBeGreaterThan(0);
        expect(healthMetrics.configStatus).toContain('Size: 10/');
      } finally {
        await cacheManager.destroy();
      }
    });
    
    it('🧹 应该在缓存满时清理最旧条目', async () => {
      const cacheManager = createTranslationCacheManager({
        maxCacheSize: 3
      });
      
      try {
        await cacheManager.set('test1', 'en', 'zh', 'google', 'result1');
        await cacheManager.set('test2', 'en', 'zh', 'google', 'result2');
        await cacheManager.set('test3', 'en', 'zh', 'google', 'result3');
        await cacheManager.set('test4', 'en', 'zh', 'google', 'result4');
        
        expect(cacheManager.getStats().size).toBe(3);
        
        const result1 = await cacheManager.get('test1', 'en', 'zh', 'google');
        const result4 = await cacheManager.get('test4', 'en', 'zh', 'google');
        
        expect(result1).toBeNull();
        expect(result4).toBe('result4');
      } finally {
        await cacheManager.destroy();
      }
    });
  });
});