/**
 * Google翻译引擎集成测试
 * 测试与翻译服务和缓存的集成
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { GoogleTranslateEngine } from '../google';
import { TranslateService } from '../../translate.service';
import { TranslateConfigManager } from '../../config';
import { translationCache } from '../../cache-manager';

// Mock browser runtime API
const mockRuntimeAPI = {
  sendMessage: vi.fn(),
  lastError: null
};

// Mock global APIs
(global as any).browser = {
  runtime: mockRuntimeAPI
};

global.chrome = {
  runtime: mockRuntimeAPI
} as any;

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock debug utilities
vi.mock('../../../../utils/debug', () => ({
  debugContent: {
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  }
}));

describe('Google翻译引擎集成测试', () => {
  let configManager: TranslateConfigManager;
  let translateService: TranslateService;
  let googleEngine: GoogleTranslateEngine;

  beforeEach(() => {
    configManager = new TranslateConfigManager();
    translateService = new TranslateService(configManager);
    
    const config = configManager.getEngineConfig('google');
    googleEngine = new GoogleTranslateEngine(config!);
    
    // 清除所有mock
    vi.clearAllMocks();
    mockFetch.mockClear();
    mockRuntimeAPI.sendMessage.mockClear();
  });

  afterEach(async () => {
    // 清理缓存
    await translationCache.clear();
    vi.clearAllMocks();
  });

  describe('服务集成测试', () => {
    it('should integrate with TranslateService correctly', async () => {
      // Mock成功的API响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const result = await translateService.translateText('Hello', {
        from: 'en',
        to: 'zh',
        engine: 'google'
      });

      expect(result).toBe('你好');
    });

    it('should work with caching system', async () => {
      // 第一次调用 - 应该调用API
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const result1 = await translateService.translateText('Hello', {
        from: 'en',
        to: 'zh',
        engine: 'google'
      });

      expect(result1).toBe('你好');
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // 第二次调用相同内容 - 应该使用缓存
      const result2 = await translateService.translateText('Hello', {
        from: 'en',
        to: 'zh',
        engine: 'google'
      });

      expect(result2).toBe('你好');
      expect(mockFetch).toHaveBeenCalledTimes(1); // 没有额外调用API

      // 验证缓存统计
      const cacheStats = translationCache.getStats();
      expect(cacheStats.hits).toBe(1);
      expect(cacheStats.misses).toBe(1);
    });

    it('should handle batch translation with mixed cache hits/misses', async () => {
      // 预先缓存一个翻译
      await translationCache.set('Hello', 'en', 'zh', 'google', '你好');

      // Mock API响应新文本
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['世界', 'World', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const result = await translateService.translateTexts(['Hello', 'World'], {
        from: 'en',
        to: 'zh',
        engine: 'google'
      });

      expect(result).toEqual(['你好', '世界']);
      expect(mockFetch).toHaveBeenCalledTimes(1); // 只为'World'调用API

      const cacheStats = translationCache.getStats();
      expect(cacheStats.hits).toBe(1); // 'Hello'来自缓存
      expect(cacheStats.misses).toBe(1); // 'World'需要API调用
    });
  });

  describe('Background Script集成测试', () => {
    it('should use background script for requests when available', async () => {
      // Mock background script成功响应
      mockRuntimeAPI.sendMessage.mockImplementation((message, callback) => {
        setTimeout(() => {
          callback({
            success: true,
            data: [[['你好', 'Hello', null, null, 10]]],
            status: 200,
            statusText: 'OK',
            headers: {}
          });
        }, 0);
      });

      const result = await googleEngine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好']);
      expect(mockRuntimeAPI.sendMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'TRANSLATE_REQUEST',
          payload: expect.objectContaining({
            url: expect.stringContaining('translate.googleapis.com'),
            options: expect.any(Object)
          })
        }),
        expect.any(Function)
      );
    });

    it('should fallback to direct fetch when background script fails', async () => {
      // Mock background script失败
      mockRuntimeAPI.sendMessage.mockImplementation((message, callback) => {
        setTimeout(() => {
          callback({
            success: false,
            error: 'Background script error'
          });
        }, 0);
      });

      // Mock direct fetch成功
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const result = await googleEngine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好']);
      expect(mockRuntimeAPI.sendMessage).toHaveBeenCalled();
      expect(mockFetch).toHaveBeenCalled(); // 降级到直接fetch
    });
  });

  describe('配置管理集成测试', () => {
    it('should respect engine configuration changes', async () => {
      // 禁用API1
      await configManager.updateApiEndpoint('google', 'google-translate-api-1', {
        enabled: false
      });

      // Mock API2响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      // 重新创建引擎以使用新配置
      const newConfig = configManager.getEngineConfig('google');
      const newEngine = new GoogleTranslateEngine(newConfig!);

      const result = await newEngine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      // 应该使用API2的POST方法
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('translate_a/t'),
        expect.objectContaining({
          method: 'POST'
        })
      );
    });

    it('should handle API priority changes', async () => {
      // 改变API优先级 (API2优先级设为1，API1设为2)
      await configManager.updateApiEndpoint('google', 'google-translate-api-2', {
        priority: 1
      });
      await configManager.updateApiEndpoint('google', 'google-translate-api-1', {
        priority: 2
      });

      // Mock API2成功
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const newConfig = configManager.getEngineConfig('google');
      const newEngine = new GoogleTranslateEngine(newConfig!);

      const result = await newEngine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      // 应该首先尝试API2
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('translate_a/t'),
        expect.any(Object)
      );
    });

    it('should handle timeout configuration', async () => {
      // 设置很短的超时时间
      await configManager.updateEngineConfig('google', {
        timeout: 1 // 1ms
      });

      // Mock延迟响应
      mockFetch.mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 100))
      );

      const newConfig = configManager.getEngineConfig('google');
      const newEngine = new GoogleTranslateEngine(newConfig!);

      await expect(
        newEngine.translateBatch(['Hello'], { from: 'en', to: 'zh' })
      ).rejects.toThrow('All Google APIs failed');
    });
  });

  describe('错误处理集成测试', () => {
    it('should handle and classify different error types', async () => {
      const testCases = [
        {
          error: new Error('timeout'),
          expectedType: 'timeout'
        },
        {
          error: new Error('network error'),
          expectedType: 'network'
        },
        {
          error: new Error('unauthorized'),
          expectedType: 'auth'
        },
        {
          error: new Error('quota exceeded'),
          expectedType: 'quota'
        }
      ];

      for (const testCase of testCases) {
        mockFetch.mockClear();
        mockFetch.mockRejectedValueOnce(testCase.error);

        try {
          await googleEngine.translateBatch(['Hello'], { from: 'en', to: 'zh' });
          expect.fail('Should have thrown an error');
        } catch (error: any) {
          expect(error.message).toContain('All Google APIs failed');
          expect(error.originalError?.message).toContain(testCase.expectedType);
        }
      }
    });

    it('should provide detailed error information for debugging', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Custom API Error'));

      try {
        await googleEngine.translateBatch(['Hello'], { from: 'en', to: 'zh' });
        expect.fail('Should have thrown an error');
      } catch (error: any) {
        expect(error.engine).toBe('google');
        expect(error.originalError).toBeInstanceOf(Error);
        expect(error.originalError.message).toBe('Custom API Error');
      }
    });
  });

  describe('性能和统计测试', () => {
    it('should track engine statistics correctly', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const initialStats = googleEngine.getStats();
      expect(initialStats.totalRequests).toBe(0);

      await googleEngine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      const finalStats = googleEngine.getStats();
      expect(finalStats.totalRequests).toBe(1);
      expect(finalStats.successCount).toBe(1);
      expect(finalStats.errorCount).toBe(0);
    });

    it('should measure translation duration', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const result = await googleEngine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.duration).toBeGreaterThan(0);
      expect(typeof result.duration).toBe('number');
    });

    it('should provide engine status information', async () => {
      const status = await googleEngine.getStatus();
      
      expect(status.name).toBe('google');
      expect(status.available).toBe(true);
      expect(status.apis).toHaveLength(2);
      expect(status.lastCheck).toBeInstanceOf(Date);
    });
  });

  describe('语言支持测试', () => {
    it('should support language detection', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const detectedLang = await googleEngine.detectLanguage('Hello world');
      expect(typeof detectedLang).toBe('string');
    });

    it('should provide supported languages list', () => {
      const languages = googleEngine.getSupportedLanguages();
      expect(Array.isArray(languages)).toBe(true);
      expect(languages).toContain('en');
      expect(languages).toContain('zh');
      expect(languages).toContain('auto');
    });

    it('should validate language codes', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      // 使用无效语言代码
      await googleEngine.translateBatch(['Hello'], {
        from: 'invalid-source',
        to: 'invalid-target'
      });

      // 应该自动修正为有效的语言代码
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringMatching(/sl=auto.*tl=zh/),
        expect.any(Object)
      );
    });
  });
});