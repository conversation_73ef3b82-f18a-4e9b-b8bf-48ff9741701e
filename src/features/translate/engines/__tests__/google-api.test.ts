/**
 * Google翻译引擎API测试
 * 测试API1和API2的降级机制
 */

import { describe, it, expect, vi, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import { GoogleTranslateEngine } from '../google';
import { GoogleEngineConfig, TranslateErrorType } from '../../types';
import { server } from '../../../../test/setup';

// Stop MSW during these tests to allow fetch mocking
beforeAll(() => {
  server.close();
});

// Mock global fetch 
const mockFetch = vi.fn();
Object.defineProperty(global, 'fetch', {
  value: mockFetch,
  writable: true
});

// Mock debug utilities
vi.mock('../../../../utils/debug', () => ({
  debugContent: {
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  }
}));

describe('GoogleTranslateEngine API Tests', () => {
  let engine: GoogleTranslateEngine;
  let mockConfig: GoogleEngineConfig;

  beforeEach(() => {
    // Mock browser/chrome APIs to prevent background script calls
    vi.stubGlobal('browser', { runtime: null });
    if (typeof global.chrome !== 'undefined') {
      Object.defineProperty(global.chrome, 'runtime', { value: null, writable: true });
    }
    mockConfig = {
      name: 'google',
      enabled: true,
      priority: 1,
      maxChunkSize: 5000,
      maxBatchSize: 128,
      timeout: 10000,
      retryCount: 2,
      apis: [
        {
          name: 'google-translate-api-1',
          url: 'https://translate-pa.googleapis.com/v1/translateHtml',
          priority: 1,
          enabled: true,
          timeout: 10000
        },
        {
          name: 'google-translate-api-2',
          url: 'https://translate.googleapis.com/translate_a/t',
          priority: 2,
          enabled: true,
          timeout: 10000
        }
      ]
    };

    engine = new GoogleTranslateEngine(mockConfig);
    mockFetch.mockClear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('API1 (POST translateHtml)', () => {
    it('should successfully translate using API1', async () => {
      // Mock API1成功响应 (translateHtml格式)
      const mockResponse = ['你好'];
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        status: 200,
        statusText: 'OK'
      });

      const result = await engine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好']);
      expect(result.engine).toBe('google');
      expect(result.api).toBe('google-translate-api-1');
      
      // 验证是POST请求到正确的端点
      expect(mockFetch).toHaveBeenCalledWith(
        'https://translate-pa.googleapis.com/v1/translateHtml',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json+protobuf',
            'x-goog-api-key': 'AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520'
          }),
          body: expect.stringContaining('Hello')
        })
      );
    });

    it('should handle API1 empty response gracefully', async () => {
      // Mock API1空响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [],
        status: 200,
        statusText: 'OK'
      });

      const result = await engine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['Hello']); // 返回原文
    });

    it('should handle API1 malformed response', async () => {
      // Mock API1畸形响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ invalid: 'format' }),
        status: 200,
        statusText: 'OK'
      });

      const result = await engine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['Hello']); // 返回原文
    });

    it('should handle API1 batch translation', async () => {
      // Mock API1批量响应 (translateHtml支持批量)
      const mockResponse = ['你好', '世界'];
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        status: 200,
        statusText: 'OK'
      });

      const result = await engine.translateBatch(['Hello', 'World'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好', '世界']);
      
      // 验证请求体包含批量文本
      expect(mockFetch).toHaveBeenCalledWith(
        'https://translate-pa.googleapis.com/v1/translateHtml',
        expect.objectContaining({
          body: expect.stringContaining('"Hello"')
        })
      );
    });
  });

  describe('API2 (POST方式)', () => {
    it('should successfully translate using API2', async () => {
      // 禁用API1，强制使用API2
      mockConfig.apis[0].enabled = false;
      engine = new GoogleTranslateEngine(mockConfig);

      // Mock API2成功响应
      const mockResponse = [[['你好', 'Hello', null, null, 10]]];
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        status: 200,
        statusText: 'OK'
      });

      const result = await engine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好']);
      expect(result.engine).toBe('google');

      // 验证是POST请求
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('translate_a/t'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/x-www-form-urlencoded'
          }),
          body: expect.stringContaining('q=Hello')
        })
      );
    });

    it('should handle API2 batch translation', async () => {
      // 禁用API1，强制使用API2
      mockConfig.apis[0].enabled = false;
      engine = new GoogleTranslateEngine(mockConfig);

      // Mock API2批量响应
      const mockResponse = [
        [['你好', 'Hello', null, null, 10]],
        [['世界', 'World', null, null, 10]]
      ];
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        status: 200,
        statusText: 'OK'
      });

      const result = await engine.translateBatch(['Hello', 'World'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好', '世界']);

      // 验证请求体包含多个q参数
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('translate_a/t'),
        expect.objectContaining({
          body: expect.stringMatching(/q=Hello.*q=World/)
        })
      );
    });
  });

  describe('降级机制测试', () => {
    it('should fallback from API1 to API2 when API1 fails', async () => {
      // API1失败，API2成功
      mockFetch
        .mockRejectedValueOnce(new Error('API1 Network Error'))
        .mockResolvedValueOnce({
          ok: true,
          json: async () => [[['你好', 'Hello', null, null, 10]]],
          status: 200,
          statusText: 'OK'
        });

      const result = await engine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好']);
      expect(mockFetch).toHaveBeenCalledTimes(2); // API1失败，然后API2成功
    });

    it('should try all APIs in priority order', async () => {
      // 所有API都失败
      mockFetch
        .mockRejectedValueOnce(new Error('API1 Error'))
        .mockRejectedValueOnce(new Error('API2 Error'));

      await expect(
        engine.translateBatch(['Hello'], { from: 'en', to: 'zh' })
      ).rejects.toThrow('All Google APIs failed');

      expect(mockFetch).toHaveBeenCalledTimes(2); // 尝试了两个API
    });

    it('should stop fallback on authentication error', async () => {
      // API1认证错误，应该停止尝试其他API
      mockFetch.mockRejectedValueOnce(new Error('unauthorized'));

      await expect(
        engine.translateBatch(['Hello'], { from: 'en', to: 'zh' })
      ).rejects.toThrow();

      expect(mockFetch).toHaveBeenCalledTimes(1); // 只尝试了API1
    });

    it('should respect API enabled/disabled status', async () => {
      // 禁用API1
      mockConfig.apis[0].enabled = false;
      engine = new GoogleTranslateEngine(mockConfig);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const result = await engine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      // 应该直接使用API2（第二个URL）
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('translate_a/t'),
        expect.any(Object)
      );
    });

    it('should throw error when no APIs are available', async () => {
      // 禁用所有API
      mockConfig.apis.forEach(api => api.enabled = false);
      engine = new GoogleTranslateEngine(mockConfig);

      await expect(
        engine.translateBatch(['Hello'], { from: 'en', to: 'zh' })
      ).rejects.toThrow('No available Google APIs');
    });
  });

  describe('请求构建测试', () => {
    it('should build correct request for API1 (translateHtml)', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ['你好'],
        status: 200,
        statusText: 'OK'
      });

      await engine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh-CN'
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'https://translate-pa.googleapis.com/v1/translateHtml',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json+protobuf',
            'x-goog-api-key': 'AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520'
          }),
          body: expect.stringContaining('"Hello"')
        })
      );
    });

    it('should build correct request for API2', async () => {
      // 禁用API1
      mockConfig.apis[0].enabled = false;
      engine = new GoogleTranslateEngine(mockConfig);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      await engine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh-CN'
      });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('translate_a/t'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/x-www-form-urlencoded'
          }),
          body: expect.stringContaining('q=Hello')
        })
      );
    });

    it('should handle special characters in text', async () => {
      const specialText = 'Hello "World" & <Test>';
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好"世界"&<测试>', specialText, null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const result = await engine.translateBatch([specialText], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://translate-pa.googleapis.com/v1/translateHtml',
        expect.objectContaining({
          body: expect.stringContaining(specialText.replace(/"/g, '\\"'))
        })
      );
    });
  });

  describe('响应解析测试', () => {
    it('should parse complex API response correctly', async () => {
      // API1应该返回简单的字符串数组格式
      const mockResponse = ['句子1翻译'];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        status: 200,
        statusText: 'OK'
      });

      const result = await engine.translateBatch(['句子1原文'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['句子1翻译']);
    });

    it('should handle HTTP error responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      });

      await expect(
        engine.translateBatch(['Hello'], { from: 'en', to: 'zh' })
      ).rejects.toThrow('All Google APIs failed');
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network Error'));

      await expect(
        engine.translateBatch(['Hello'], { from: 'en', to: 'zh' })
      ).rejects.toThrow('All Google APIs failed');
    });
  });

  describe('语言验证测试', () => {
    it('should validate and correct invalid language codes', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      const result = await engine.translateBatch(['Hello'], {
        from: 'invalid-lang',
        to: 'invalid-target'
      });

      expect(result.success).toBe(true);
      // 验证请求体中的语言参数被修正
      expect(mockFetch).toHaveBeenCalledWith(
        'https://translate-pa.googleapis.com/v1/translateHtml',
        expect.objectContaining({
          body: expect.stringContaining('"auto","zh"')
        })
      );
    });

    it('should support auto language detection', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [[['你好', 'Hello', null, null, 10]]],
        status: 200,
        statusText: 'OK'
      });

      await engine.translateBatch(['Hello'], {
        from: 'auto',
        to: 'zh'
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'https://translate-pa.googleapis.com/v1/translateHtml',
        expect.objectContaining({
          body: expect.stringContaining('"auto"')
        })
      );
    });
  });

  describe('分块处理测试', () => {
    it('should handle large text batches correctly', async () => {
      const largeTexts = Array(15).fill('Hello').map((text, i) => `${text} ${i}`);
      
      // Mock API1批量响应 (translateHtml支持批量)
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => Array(15).fill('你好'),
        status: 200
      });

      const result = await engine.translateBatch(largeTexts, {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toHaveLength(15);
      // API1支持批量处理，只需要一次调用
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });
  });
});