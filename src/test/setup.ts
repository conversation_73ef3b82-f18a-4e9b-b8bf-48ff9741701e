import '@testing-library/jest-dom';
import { beforeAll, afterEach, afterAll, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { handlers } from './mocks/handlers';

// MSW server setup
export const server = setupServer(...handlers);

// Mock browser extension APIs - configurable for tests
if (typeof (globalThis as any).browser === 'undefined') {
  Object.defineProperty(globalThis, 'browser', {
    value: {
      storage: {
        local: {
          get: vi.fn().mockResolvedValue({}),
          set: vi.fn().mockResolvedValue(undefined),
          remove: vi.fn().mockResolvedValue(undefined),
          clear: vi.fn().mockResolvedValue(undefined)
        }
      },
      runtime: {
        getURL: vi.fn((path: string) => `chrome-extension://test-id/${path}`)
      }
    },
    configurable: true,
    writable: true
  });
}

// Mock chrome API (fallback for browser API)
Object.defineProperty(global, 'chrome', {
  value: (globalThis as any).browser
});

// Mock DOM APIs that might be missing in test environment
Object.defineProperty(global, 'ResizeObserver', {
  value: class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
});

// Setup and teardown
beforeAll(() => {
  server.listen({ onUnhandledRequest: 'error' });
});

afterEach(() => {
  cleanup();
  server.resetHandlers();
});

afterAll(() => {
  server.close();
});