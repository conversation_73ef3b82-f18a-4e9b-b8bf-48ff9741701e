/**
 * 高亮系统统一配置管理
 * 集中管理所有高亮相关的配置选项
 */

// === 性能配置 ===
export const HIGHLIGHT_PERFORMANCE_CONFIG = {
  // DOM操作配置
  BATCH_SIZE: 50,                    // 批量处理大小
  DEBOUNCE_DELAY: 100,              // 防抖延迟(ms)
  MAX_CONCURRENT_HIGHLIGHTS: 10,     // 最大并发高亮数
  VIEWPORT_BUFFER: 500,             // 视窗缓冲区(px)

  // 请求配置
  IDLE_CALLBACK_TIMEOUT: 100,       // requestIdleCallback超时
  BATCH_PROCESSING_DELAY: 0,        // 批处理延迟

  // 内存管理
  MAX_CACHED_REGEX: 100,            // 最大缓存正则表达式数
  CLEANUP_INTERVAL: 300000,         // 清理间隔(5分钟)
} as const;

// === 存储配置 ===
export const HIGHLIGHT_STORAGE_CONFIG = {
  // 存储键配置
  KEY_PREFIX: 'lucid-highlight-',
  WORD_COUNTS_KEY: 'lucid-word-counts',
  BACKUP_KEY: '__lucid_backup__',
  HEALTH_CHECK_KEY: '__lucid_health_check__',

  // 存储策略
  BACKUP_INTERVAL: 300000,          // 备份间隔(5分钟)
  MAX_STORAGE_SIZE: 1024 * 1024,    // 最大存储大小(1MB)
  RETRY_ATTEMPTS: 3,                // 重试次数
  RETRY_DELAY: 1000,                // 重试延迟(ms)

  // 健康检查
  HEALTH_CHECK_INTERVAL: 60000,     // 健康检查间隔(1分钟)
  HEALTH_CHECK_TIMEOUT: 5000,       // 健康检查超时
} as const;

// === UI配置 ===
export const HIGHLIGHT_UI_CONFIG = {
  // 动画配置
  ANIMATION_DURATION: 300,          // 动画持续时间(ms)
  HOVER_DELAY: 200,                 // 悬停延迟(ms)
  FADE_DURATION: 150,               // 淡入淡出时间

  // 提示框配置
  MAX_TOOLTIP_WIDTH: 300,           // 最大提示框宽度
  TOOLTIP_OFFSET: 10,               // 提示框偏移
  TOOLTIP_Z_INDEX: 10001,           // 提示框层级

  // 高亮样式
  DEFAULT_EFFECTS: ['lu-gradient', 'lu-underline'],
  MAX_HIGHLIGHT_LEVEL: 5,           // 最大高亮级别
  LEVEL_THRESHOLD: [1, 3, 6, 10, 20], // 级别阈值
} as const;

// === 文本处理配置 ===
export const HIGHLIGHT_TEXT_CONFIG = {
  // 词汇过滤
  MIN_WORD_LENGTH: 2,               // 最小词汇长度
  MAX_WORD_LENGTH: 30,              // 最大词汇长度

  // 黑名单词汇
  BLACKLISTED_WORDS: [
    'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
    'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must',
    'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
  ],

  // 排除元素
  EXCLUDED_ELEMENTS: [
    'SCRIPT', 'STYLE', 'NOSCRIPT', 'TEXTAREA', 'INPUT', 'SELECT',
    'CODE', 'PRE', 'LUCID-HIGHLIGHT'
  ],

  // 排除类名
  EXCLUDED_CLASSES: [
    'lucid-highlight', 'no-highlight', 'code-block'
  ],
} as const;

// === 错误处理配置 ===
export const HIGHLIGHT_ERROR_CONFIG = {
  // 错误级别
  LOG_LEVEL: 'INFO' as 'DEBUG' | 'INFO' | 'WARN' | 'ERROR',

  // 错误重试
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_BACKOFF_FACTOR: 2,
  INITIAL_RETRY_DELAY: 1000,

  // 错误报告
  ENABLE_ERROR_REPORTING: false,
  ERROR_REPORT_ENDPOINT: '',

  // 降级策略
  ENABLE_GRACEFUL_DEGRADATION: true,
  FALLBACK_TO_MEMORY_STORAGE: true,
} as const;

// === 开发配置 ===
export const HIGHLIGHT_DEV_CONFIG = {
  // 调试选项
  ENABLE_DEBUG_LOGS: true,
  ENABLE_PERFORMANCE_MONITORING: true,
  ENABLE_MEMORY_MONITORING: false,

  // 测试选项
  ENABLE_TEST_MODE: false,
  MOCK_STORAGE_FAILURES: false,
  SIMULATE_SLOW_NETWORK: false,

  // 开发工具
  EXPOSE_GLOBAL_HELPERS: true,
  ENABLE_CONSOLE_COMMANDS: true,
} as const;

// === 主配置对象 ===
export const HIGHLIGHT_CONFIG = {
  PERFORMANCE: HIGHLIGHT_PERFORMANCE_CONFIG,
  STORAGE: HIGHLIGHT_STORAGE_CONFIG,
  UI: HIGHLIGHT_UI_CONFIG,
  TEXT: HIGHLIGHT_TEXT_CONFIG,
  ERROR: HIGHLIGHT_ERROR_CONFIG,
  DEV: HIGHLIGHT_DEV_CONFIG,
} as const;

// === 配置类型定义 ===
export type HighlightPerformanceConfig = typeof HIGHLIGHT_PERFORMANCE_CONFIG;
export type HighlightStorageConfig = typeof HIGHLIGHT_STORAGE_CONFIG;
export type HighlightUIConfig = typeof HIGHLIGHT_UI_CONFIG;
export type HighlightTextConfig = typeof HIGHLIGHT_TEXT_CONFIG;
export type HighlightErrorConfig = typeof HIGHLIGHT_ERROR_CONFIG;
export type HighlightDevConfig = typeof HIGHLIGHT_DEV_CONFIG;
export type HighlightConfig = typeof HIGHLIGHT_CONFIG;

// === 配置管理器 ===
export class HighlightConfigManager {
  private static instance: HighlightConfigManager;
  private config: HighlightConfig = HIGHLIGHT_CONFIG;
  private overrides: Partial<HighlightConfig> = {};

  static getInstance(): HighlightConfigManager {
    if (!this.instance) {
      this.instance = new HighlightConfigManager();
    }
    return this.instance;
  }

  /**
   * 获取配置值
   */
  get<K extends keyof HighlightConfig>(section: K): HighlightConfig[K] {
    return { ...this.config[section], ...this.overrides[section] };
  }

  /**
   * 设置配置覆盖
   */
  override<K extends keyof HighlightConfig>(
    section: K,
    overrides: Partial<HighlightConfig[K]>
  ): void {
    this.overrides[section] = { ...this.overrides[section], ...overrides };
  }

  /**
   * 重置配置
   */
  reset(): void {
    this.overrides = {};
  }

  /**
   * 获取完整配置
   */
  getFullConfig(): HighlightConfig {
    return {
      PERFORMANCE: this.get('PERFORMANCE'),
      STORAGE: this.get('STORAGE'),
      UI: this.get('UI'),
      TEXT: this.get('TEXT'),
      ERROR: this.get('ERROR'),
      DEV: this.get('DEV'),
    };
  }
}

// 导出单例实例
export const highlightConfigManager = HighlightConfigManager.getInstance();

// 便捷访问函数
export const getHighlightConfig = () => highlightConfigManager.getFullConfig();
export const getPerformanceConfig = () => highlightConfigManager.get('PERFORMANCE');
export const getStorageConfig = () => highlightConfigManager.get('STORAGE');
export const getUIConfig = () => highlightConfigManager.get('UI');
export const getTextConfig = () => highlightConfigManager.get('TEXT');
export const getErrorConfig = () => highlightConfigManager.get('ERROR');
export const getDevConfig = () => highlightConfigManager.get('DEV');