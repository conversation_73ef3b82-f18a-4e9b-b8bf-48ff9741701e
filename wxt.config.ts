import { resolve } from "path";
import { defineConfig, type ConfigEnv } from "wxt";

// See https://wxt.dev/api/config.html
export default defineConfig({
  modules: ['@wxt-dev/module-react'],
  browser: "chromium",
  // browser: "firefox",
  // WXT官方推荐的多路径别名配置
  alias: {
    '@components': resolve('src/components'),
    '@features': resolve('src/features'),
    '@services': resolve('src/services'),
    '@ui-manager': resolve('src/ui-manager'),
    '@styles': resolve('src/styles'),
    '@test': resolve('src/test'),
    // 具体的子目录别名，便于快速导入
    '@tooltip': resolve('src/components/Tooltip'),
    '@dynamic-tooltip': resolve('src/components/DynamicTooltip'),
    '@dictionary': resolve('src/features/dictionary'),
  },

  vite: (env: ConfigEnv) => ({
    server: {
      proxy: {
        '/api': {
          target: 'http://localhost:3003',
          changeOrigin: true,
        },
      },
    },
  }),
  manifest: {
    name: "Lucid Extension",
    description: "智能高亮与查词浏览器扩展",
    permissions: ["storage", "tts", "contextMenus", "activeTab"],
    host_permissions: [
      "<all_urls>",
      "http://localhost:*/*",
      "https://localhost:*/*",
      "https://translate.googleapis.com/*",
      "https://translate-pa.googleapis.com/*"
    ],
    action: {
      default_title: "Lucid",
    },
    commands: {
      "toggle-transparent-popup": {
        suggested_key: {
          default: "Ctrl+Shift+L",
          mac: "Command+Shift+L",
        },
        description: "切换 Lucid 滑动面板",
      },
      "highlight-selection": {
        suggested_key: {
          default: "Ctrl+Shift+H",
          mac: "Command+Shift+H",
        },
        description: "高亮选中文本",
      },
    },
    web_accessible_resources: [
      // CSS 资源用于 Shadow DOM 样式注入
      {
        resources: ["assets/styles/*.css", "content-scripts/content.css"],
        matches: ["<all_urls>"],
      }
    ],
  }
});
