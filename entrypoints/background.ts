// debugBackground 导入已移除，使用手动 console.log
import { backgroundErrorHandler } from '../src/utils/error-handler';

// 消息处理器接口
interface MessageHandlers {
  [key: string]: (message: any, sender: any, sendResponse: any) => boolean | void | Promise<any>;
}

// 消息验证器
function validateMessage(message: any, sender: any, messageRateLimit: Map<string, number>): boolean {
  // SECURITY: Enhanced message validation with allowedAction pattern
  const ALLOWED_MESSAGE_TYPES = ['DICTIONARY_API_REQUEST', 'HEALTH_CHECK', 'TRANSLATE_REQUEST', 'TRANSLATE_PAGE_REQUEST', 'PING'];

  // Validate message type
  if (!message.type || !ALLOWED_MESSAGE_TYPES.includes(message.type)) {
    console.log('⚠️ [background-service|WARN] Unknown or missing message type:', message.type);
    return false;
  }

  // Enhanced sender validation
  if (sender.tab && sender.tab.url) {
    // Only allow messages from HTTP/HTTPS pages or extension pages
    const url = sender.tab.url;
    if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('chrome-extension://')) {
      console.log('⚠️ [background-service|WARN] Message from invalid protocol, ignoring:', url);
      return false;
    }
  }

  // Rate limiting: Basic protection against message spam
  const senderId = sender.tab?.id || 'popup';
  const now = Date.now();
  const rateKey = `${senderId}-${message.type}`;
  const lastRequest = messageRateLimit.get(rateKey);

  if (lastRequest && now - lastRequest < 100) { // 100ms minimum between requests
    console.log('⚠️ [background-service|WARN] Rate limit exceeded for message type:', message.type);
    return false;
  }

  messageRateLimit.set(rateKey, now);
  return true;
}

// 词典API处理器
function createDictionaryHandler() {
  return (message: any, _sender: any, sendResponse: any) => {
    (async () => {
      try {
        // Validate payload
        if (!message.payload || typeof message.payload.word !== 'string') {
          throw backgroundErrorHandler.validation('Invalid payload: missing or invalid word');
        }

        const { word } = message.payload;

        // Basic word validation
        if (!word || word.trim().length === 0 || word.length > 100) {
          throw backgroundErrorHandler.validation('Invalid word: empty or too long');
        }

        const url = `http://localhost:3003/api/dictionary/en/${encodeURIComponent(word)}`;
        console.log('✅ [background-service|INFO] Fetching dictionary data for:', word);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });

        if (!response.ok) {
          throw backgroundErrorHandler.network(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('✅ [background-service|INFO] Dictionary data received for:', word);

        sendResponse({ success: true, data });
      } catch (error) {
        const standardError = backgroundErrorHandler.api('Dictionary API failed', {
          originalError: error,
          word: message.payload?.word
        });
        sendResponse({
          success: false,
          error: standardError.message
        });
      }
    })();

    return true; // Indicate async response
  };
}

// 健康检查处理器
function createHealthCheckHandler() {
  return (_message: any, _sender: any, sendResponse: any) => {
    (async () => {
      try {
        const response = await fetch('http://localhost:3003/api/health', {
          method: 'GET',
          headers: { 'Accept': 'application/json' }
        });
        sendResponse({ success: response.ok });
      } catch (error) {
        sendResponse({ success: false });
      }
    })();

    return true; // Indicate async response
  };
}

// PING处理器
function createPingHandler() {
  return (message: any, _sender: any, sendResponse: any) => {
    console.log('✅ [background-service|INFO] Responding to PING', {
      originalTimestamp: message.timestamp,
      responseTimestamp: Date.now()
    });
    
    // 立即响应，不使用异步
    try {
      sendResponse({
        success: true,
        message: 'PONG',
        timestamp: Date.now(),
        backgroundId: browser.runtime.id,
        originalTimestamp: message.timestamp
      });
    } catch (error) {
      console.log('❌ [background-service|ERROR] Failed to send PING response:', error);
    }
    
    return false; // 同步响应，不需要保持消息端口
  };
}

// 翻译请求处理器
function createTranslateHandler() {
  return (message: any, _sender: any, sendResponse: any) => {
    (async () => {
      try {
        // Validate payload
        if (!message.payload || !message.payload.url || !message.payload.options) {
          throw backgroundErrorHandler.validation('Invalid payload: missing url or options');
        }

        const { url, options } = message.payload;

        // Basic URL validation
        if (!url || typeof url !== 'string' || url.length === 0) {
          throw backgroundErrorHandler.validation('Invalid URL');
        }

        // SECURITY: Domain whitelist validation to prevent CORS proxy abuse
        const ALLOWED_DOMAINS = ['translate.googleapis.com', 'translate-pa.googleapis.com'];
        try {
          const urlObj = new URL(url);
          if (!ALLOWED_DOMAINS.includes(urlObj.hostname)) {
            throw backgroundErrorHandler.validation(`Domain not allowed: ${urlObj.hostname}. Only ${ALLOWED_DOMAINS.join(', ')} are permitted.`);
          }
        } catch (urlError) {
          throw backgroundErrorHandler.validation('Invalid URL format');
        }

        console.log('✅ [background-service|INFO] Proxying translation request to:', url, {
          originalHeaders: options.headers,
          method: options.method
        });

        // Build request options
        // 🔧 修复CORS问题：不要覆盖翻译引擎指定的headers
        const requestOptions = {
          method: options.method || 'GET',
          headers: {
            // 只有在翻译引擎没有指定时才使用默认headers
            ...(options.headers && Object.keys(options.headers).length > 0 
              ? options.headers 
              : {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json'
                })
          },
          body: options.body || undefined
        };

        console.log('✅ [background-service|INFO] Final request options:', {
          method: requestOptions.method,
          headers: requestOptions.headers,
          bodyLength: requestOptions.body ? requestOptions.body.toString().length : 0
        });

        const response = await fetch(url, requestOptions);

        if (!response.ok) {
          throw backgroundErrorHandler.network(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        const responseHeaders: Record<string, string> = {};
        response.headers.forEach((value, key) => {
          responseHeaders[key] = value;
        });

        console.log('✅ [background-service|INFO] Translation request completed successfully');

        sendResponse({
          success: true,
          data,
          status: response.status,
          statusText: response.statusText,
          headers: responseHeaders
        });
      } catch (error) {
        const standardError = backgroundErrorHandler.api('Translation request failed', {
          originalError: error,
          url: message.payload?.url
        });
        sendResponse({
          success: false,
          error: standardError.message
        });
      }
    })();

    return true; // Indicate async response
  };
}

// 消息处理器工厂
function createMessageHandlers(): MessageHandlers {
  return {
    'DICTIONARY_API_REQUEST': createDictionaryHandler(),
    'HEALTH_CHECK': createHealthCheckHandler(),
    'PING': createPingHandler(),
    'TRANSLATE_REQUEST': createTranslateHandler()
  };
}

// 主消息监听器
/**
 * Service Worker 生命周期管理 - Manifest V3 最佳实践
 * 
 * 使用定时器和消息监听器保持Service Worker活跃，
 * 避免自连接端口导致的"Receiving end does not exist"错误。
 */
function setupServiceWorkerLifecycle() {
  let keepAliveTimer: NodeJS.Timeout | null = null;
  let isActive = true;
  let lifecycleCleanup: (() => void) | null = null;

  console.log('✅ [background-service|INFO] Setting up Service Worker lifecycle management (timer-based)');

  // 清理函数
  const cleanup = () => {
    console.log('✅ [background-service|INFO] Cleaning up Service Worker lifecycle management');
    isActive = false;
    if (keepAliveTimer) {
      clearInterval(keepAliveTimer);
      keepAliveTimer = null;
    }
  };

  // 使用定时器保持Service Worker活跃
  function startKeepAliveTimer() {
    // 如果已有定时器，先清除
    if (keepAliveTimer) {
      clearInterval(keepAliveTimer);
      keepAliveTimer = null;
    }

    if (!isActive) {
      console.log('🔧 [background-service|DEBUG] Service Worker not active, skipping timer start');
      return;
    }

    // 每25秒执行一次轻量级操作保持活跃
    keepAliveTimer = setInterval(() => {
      if (!isActive) {
        console.log('🔧 [background-service|DEBUG] Service Worker inactive, clearing timer');
        if (keepAliveTimer) {
          clearInterval(keepAliveTimer);
          keepAliveTimer = null;
        }
        return;
      }

      // 执行轻量级操作保持Service Worker活跃
      try {
        // 简单的chrome.storage读取操作
        chrome.storage.local.get(['keepAlive'], () => {
          if (chrome.runtime.lastError) {
            console.log('🔧 [background-service|DEBUG] Keep-alive storage check completed with error (expected)');
          } else {
            console.log('🔧 [background-service|DEBUG] Keep-alive timer tick - Service Worker active');
          }
        });
      } catch (error) {
        console.log('🔧 [background-service|DEBUG] Keep-alive timer error (non-critical):', error);
      }
    }, 25000); // 25秒间隔，低于Chrome的30秒限制

    console.log('🔧 [background-service|DEBUG] Keep-alive timer started (25s interval)');
  }

  // 监听扩展安装和启动事件
  function setupLifecycleListeners() {
    // 监听扩展安装
    if (chrome.runtime.onInstalled) {
      chrome.runtime.onInstalled.addListener(() => {
        console.log('✅ [background-service|INFO] Extension installed/updated - starting keep-alive');
        isActive = true;
        startKeepAliveTimer();
      });
    }

    // 监听扩展启动
    if (chrome.runtime.onStartup) {
      chrome.runtime.onStartup.addListener(() => {
        console.log('✅ [background-service|INFO] Extension startup - starting keep-alive');
        isActive = true;
        startKeepAliveTimer();
      });
    }

    // 监听消息以保持活跃
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      // 任何消息都会重置定时器
      if (isActive && message.type) {
        console.log('🔧 [background-service|DEBUG] Message received, Service Worker is active:', message.type);
        // 消息处理会自然保持Service Worker活跃，不需要额外操作
      }
      // 不拦截消息，让其他处理器继续处理
      return false;
    });

    console.log('🔧 [background-service|DEBUG] Lifecycle listeners established');
  }

  // 处理Service Worker suspend事件（如果支持）
  if (typeof self !== 'undefined' && 'addEventListener' in self) {
    (self as any).addEventListener('install', (event: any) => {
      console.log('✅ [background-service|INFO] Service Worker installing');
      if (event.waitUntil && (self as any).skipWaiting) {
        event.waitUntil((self as any).skipWaiting());
      }
    });

    (self as any).addEventListener('activate', (event: any) => {
      console.log('✅ [background-service|INFO] Service Worker activated');
      if (event.waitUntil && (self as any).clients) {
        event.waitUntil((self as any).clients.claim());
      }
      
      // 激活后立即启动keep-alive
      isActive = true;
      startKeepAliveTimer();
    });

    // 监听Service Worker即将被suspend的事件
    (self as any).addEventListener('beforeunload', cleanup);
    
    // 监听pagehide事件以确保清理
    (self as any).addEventListener('pagehide', cleanup);
  }

  // 在全局对象上监听卸载事件
  if (typeof globalThis !== 'undefined') {
    globalThis.addEventListener('beforeunload', cleanup);
  }

  // 设置清理函数引用
  lifecycleCleanup = cleanup;

  // 初始化
  setupLifecycleListeners();
  startKeepAliveTimer();

  console.log('✅ [background-service|INFO] Service Worker lifecycle management initialized (no self-connect)');

  // 返回清理函数
  return cleanup;
}

/**
 * 设置 Background Script 调试工具
 * 在 background script 控制台中提供调试接口
 */
function setupBackgroundDebugTools() {
  console.log('✅ [background-service|INFO] Setting up background debug tools');

  const globalObj = globalThis as any;

  // Background Script 调试工具
  globalObj.LucidBackgroundDebug = {
    // 状态查询
    getStatus() {
      return {
        extensionId: browser.runtime.id,
        isBackgroundScript: true,
        environment: 'background',
        timestamp: Date.now()
      };
    },

    // 测试与content script的连接
    async testContentConnection(tabId?: number) {
      try {
        const tabs = await browser.tabs.query({ active: true, currentWindow: true });
        const targetTabId = tabId || tabs[0]?.id;
        
        if (!targetTabId) {
          throw new Error('No active tab found');
        }

        console.log(`✅ [background-service|INFO] Testing connection to tab ${targetTabId}`);
        
        const response = await browser.tabs.sendMessage(targetTabId, {
          type: 'PING',
          timestamp: Date.now(),
          source: 'background-debug'
        });

        console.log('✅ [background-service|INFO] Content script connection test result:', response);
        return { success: true, response, tabId: targetTabId };
      } catch (error) {
        console.log('❌ [background-service|ERROR] Content script connection test failed:', error);
        return { success: false, error: (error as Error).message };
      }
    },

    // 获取所有标签页
    async getAllTabs() {
      try {
        const tabs = await browser.tabs.query({});
        const tabInfo = tabs.map(tab => ({
          id: tab.id,
          url: tab.url,
          title: tab.title,
          active: tab.active
        }));
        
        console.log('✅ [background-service|INFO] All tabs:', tabInfo);
        return tabInfo;
      } catch (error) {
        console.log('❌ [background-service|ERROR] Failed to get tabs:', error);
        return [];
      }
    },

    // 测试翻译请求
    async testTranslateRequest() {
      try {
        const response = await fetch('https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=zh&dt=t&q=background test', {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        const translation = data[0]?.[0]?.[0] || 'No translation';
        
        console.log('✅ [background-service|INFO] Translation test result:', { original: 'background test', translation });
        return { success: true, translation, data };
      } catch (error) {
        console.log('❌ [background-service|ERROR] Translation test failed:', error);
        return { success: false, error: (error as Error).message };
      }
    },

    // 发送消息到content script
    async sendToContent(message: any, tabId?: number) {
      try {
        const tabs = await browser.tabs.query({ active: true, currentWindow: true });
        const targetTabId = tabId || tabs[0]?.id;
        
        if (!targetTabId) {
          throw new Error('No active tab found');
        }

        const response = await browser.tabs.sendMessage(targetTabId, message);
        console.log('✅ [background-service|INFO] Message sent to content script:', { message, response });
        return { success: true, response };
      } catch (error) {
        console.log('❌ [background-service|ERROR] Failed to send message to content script:', error);
        return { success: false, error: (error as Error).message };
      }
    }
  };

  console.log('✅ [background-service|INFO] Background debug tools ready - use LucidBackgroundDebug object');
  console.log('🛠️ Background Script调试工具已就绪');
  console.log('📋 可用命令:');
  console.log('  • LucidBackgroundDebug.getStatus() - 查看Background Script状态');
  console.log('  • LucidBackgroundDebug.testContentConnection() - 测试与Content Script连接');
  console.log('  • LucidBackgroundDebug.getAllTabs() - 获取所有标签页');
  console.log('  • LucidBackgroundDebug.testTranslateRequest() - 测试翻译请求');
  console.log('  • LucidBackgroundDebug.sendToContent({type: "PING"}) - 发送消息到Content Script');
}

function setupMessageListener(messageHandlers: MessageHandlers, messageRateLimit: Map<string, number>) {
  browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('✅ [background-service|INFO] Received message:', message.type, {
      sender: sender.tab?.url || 'popup/background',
      messageId: message.id || 'no-id',
      timestamp: Date.now()
    });

    // 验证消息
    if (!validateMessage(message, sender, messageRateLimit)) {
      return;
    }

    // 路由到相应的处理器
    const handler = messageHandlers[message.type];
    if (handler) {
      // ⚡ 改进：确保异步处理器正确返回 true 来保持消息端口开放
      try {
        const result = handler(message, sender, sendResponse);
        
        // 如果处理器返回 true，表示将异步响应
        if (result === true) {
          console.log('🔧 [background-service|DEBUG] Handler will respond asynchronously, keeping port open');
          return true;
        }
        
        // 如果处理器返回 Promise，也需要保持端口开放
        if (result && typeof result === 'object' && 'then' in result && typeof result.then === 'function') {
          console.log('🔧 [background-service|DEBUG] Handler returned Promise, keeping port open');
          (result as Promise<any>).catch((error: any) => {
            console.log('❌ [background-service|ERROR] Async handler error:', error);
            sendResponse({ success: false, error: error.message });
          });
          return true;
        }
        
        return result;
      } catch (error) {
        console.log('❌ [background-service|ERROR] Message handler error:', error);
        sendResponse({ success: false, error: (error as Error).message });
        return false;
      }
    }

    console.log('⚠️ [background-service|WARN] No handler found for message type:', message.type);
    sendResponse({ success: false, error: `Unknown message type: ${message.type}` });
    return false;
  });
}

export default defineBackground(() => {

  console.log('✅ [background-service|STARTUP] Background script initialized', { id: browser.runtime.id });

  // 设置Background Script调试工具
  setupBackgroundDebugTools();

  // Rate limiting storage for message spam protection
  const messageRateLimit = new Map<string, number>();

  // 消息处理器工厂
  const messageHandlers = createMessageHandlers();

  // 创建右键菜单
  browser.runtime.onInstalled.addListener(() => {
    console.log('🔧 [background-service|DEBUG] 创建右键菜单...');

    // Lucid设置菜单
    browser.contextMenus.create({
      id: 'lucid-slider',
      title: '打开 Lucid 设置',
      contexts: ['page', 'selection']
    });
    console.log('🔧 [background-service|DEBUG] 菜单创建: lucid-slider');

    // 翻译网页菜单
    browser.contextMenus.create({
      id: 'translate-page',
      title: '翻译网页',
      contexts: ['page', 'selection']
    });
    console.log('🔧 [background-service|DEBUG] 菜单创建: translate-page');


    // Mock翻译设置菜单分组
    browser.contextMenus.create({
      id: 'mock-translation-separator',
      type: 'separator',
      contexts: ['page']
    });

    browser.contextMenus.create({
      id: 'toggle-mock-translation',
      title: '🔧 切换Mock翻译',
      contexts: ['page']
    });
    console.log('🔧 [background-service|DEBUG] 菜单创建: toggle-mock-translation');



    console.log('✅ [background-service|INFO] Context menus created');
    console.log('🔧 [background-service|DEBUG] 所有右键菜单创建完成');
  });

  // 处理右键菜单点击
  browser.contextMenus.onClicked.addListener((info, tab) => {
    console.log('🔧 [background-service|DEBUG] 右键菜单点击事件', { menuItemId: info.menuItemId, tabId: tab?.id });

    if (info.menuItemId === 'lucid-slider' && tab?.id) {
      browser.tabs.sendMessage(tab.id, {
        type: 'TOGGLE_LUCID_SLIDER'
      }).catch((error) => {
        backgroundErrorHandler.runtime('Failed to send message to content script', { error });
      });
      console.log('✅ [background-service|INFO] Context menu clicked, toggling slider');
    }

    if (info.menuItemId === 'translate-page' && tab?.id) {
      // 翻译整个网页（轻量化系统）
      console.log('✅ [background-service|INFO] Context menu clicked, starting page translation with lightweight system');
      console.log('🔧 [background-service|DEBUG] 右键菜单点击 - 翻译网页', { tabId: tab.id });

      browser.tabs.sendMessage(tab.id, {
        type: 'TRANSLATE_PAGE_REQUEST',
        payload: {
          targetLanguage: 'zh-CN'
        }
      }).then((response) => {
        console.log('✅ [background-service|INFO] Page translation request sent successfully', response);
      }).catch((error) => {
        console.log('❌ [background-service|ERROR] 页面翻译消息发送失败:', error);
        backgroundErrorHandler.runtime('Failed to send page translation message to content script', { error });
      });
    }


    // 处理Mock翻译设置菜单
    if (info.menuItemId === 'toggle-mock-translation' && tab?.id) {
      console.log('✅ [background-service|INFO] Context menu clicked, toggling mock translation');
      browser.tabs.sendMessage(tab.id, {
        type: 'TOGGLE_MOCK_TRANSLATION'
      }).catch((error) => {
        backgroundErrorHandler.runtime('Failed to send toggle mock translation message', { error });
      });
    }


  });

  // 处理扩展图标点击
  browser.action.onClicked.addListener((tab) => {
    if (tab?.id) {
      browser.tabs.sendMessage(tab.id, {
        type: 'TOGGLE_LUCID_SLIDER'
      }).catch((error) => {
        backgroundErrorHandler.runtime('Failed to send message to content script', { error });
      });
      console.log('✅ [background-service|INFO] Extension icon clicked, toggling slider');
    }
  });

  // 设置消息监听器
  setupMessageListener(messageHandlers, messageRateLimit);

  // ⚡ 新增：Service Worker 生命周期管理和稳定性增强
  const cleanupKeepAlive = setupServiceWorkerLifecycle();
});
